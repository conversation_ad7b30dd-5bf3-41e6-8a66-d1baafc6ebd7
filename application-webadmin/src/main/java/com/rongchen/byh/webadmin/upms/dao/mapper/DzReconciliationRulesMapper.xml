<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.webadmin.upms.dao.DzReconciliationRulesMapper">
  <insert id="insertSelective" keyColumn="id" keyProperty="id"
    parameterType="com.rongchen.byh.webadmin.upms.model.DzReconciliationRulesEntity"
    useGeneratedKeys="true">
    <!--@mbg.generated--> insert into `dz_reconciliation_rules` <trim prefix="(" suffix=")"
      suffixOverrides=",">
      <if test="ruleId != null"> `rule_id`, </if>
      <if test="version != null"> `version`, </if>
      <if
        test="channelCode != null"> `channel_code`, </if>
      <if test="transactionType != null">
    `transaction_type`, </if>
      <if test="ruleType != null"> `rule_type`, </if>
      <if
        test="ruleContentJson != null"> `rule_content_json`, </if>
      <if test="status != null">
    `status`, </if>
      <if test="description != null"> `description`, </if>
      <if test="createTime != null">
    `create_time`, </if>
      <if test="updateTime != null"> `update_time`, </if>
    </trim>
    <trim
      prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ruleId != null"> #{ruleId,jdbcType=VARCHAR}, </if>
      <if test="version != null">
    #{version,jdbcType=INTEGER}, </if>
      <if test="channelCode != null">
    #{channelCode,jdbcType=VARCHAR}, </if>
      <if test="transactionType != null">
    #{transactionType,jdbcType=VARCHAR}, </if>
      <if test="ruleType != null">
    #{ruleType,jdbcType=VARCHAR}, </if>
      <if test="ruleContentJson != null">
    #{ruleContentJson,jdbcType=LONGVARCHAR}, </if>
      <if test="status != null">
    #{status,jdbcType=VARCHAR}, </if>
      <if test="description != null">
    #{description,jdbcType=VARCHAR}, </if>
      <if test="createTime != null">
    #{createTime,jdbcType=TIMESTAMP}, </if>
      <if test="updateTime != null">
    #{updateTime,jdbcType=TIMESTAMP}, </if>
    </trim>
  </insert>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map"
    useGeneratedKeys="true">
    <!--@mbg.generated--> insert into `dz_reconciliation_rules` (`rule_id`, `version`,
    `channel_code`, `transaction_type`, `rule_type`, `rule_content_json`, `status`, `description`,
    `create_time`, `update_time`) values <foreach collection="list" item="item" separator=",">
    (#{item.ruleId,jdbcType=VARCHAR}, #{item.version,jdbcType=INTEGER},
    #{item.channelCode,jdbcType=VARCHAR}, #{item.transactionType,jdbcType=VARCHAR},
    #{item.ruleType,jdbcType=VARCHAR}, #{item.ruleContentJson,jdbcType=LONGVARCHAR},
    #{item.status,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR},
    #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}) </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" keyColumn="id" keyProperty="id" parameterType="map"
    useGeneratedKeys="true">
    <!--@mbg.generated--> insert into `dz_reconciliation_rules` (`rule_id`, `version`,
    `channel_code`, `transaction_type`, `rule_type`, `rule_content_json`, `status`, `description`,
    `create_time`, `update_time`) values <foreach collection="list" item="item" separator=",">
    (#{item.ruleId,jdbcType=VARCHAR}, #{item.version,jdbcType=INTEGER},
    #{item.channelCode,jdbcType=VARCHAR}, #{item.transactionType,jdbcType=VARCHAR},
    #{item.ruleType,jdbcType=VARCHAR}, #{item.ruleContentJson,jdbcType=LONGVARCHAR},
    #{item.status,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR},
    #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}) </foreach> on
    duplicate key update rule_id=values(rule_id), version=values(version),
    channel_code=values(channel_code), transaction_type=values(transaction_type),
    rule_type=values(rule_type), rule_content_json=values(rule_content_json), status=values(status),
    description=values(description), create_time=values(create_time),
    update_time=values(update_time) </insert>
  <resultMap id="BaseResultMap"
    type="com.rongchen.byh.webadmin.upms.model.DzReconciliationRulesEntity">
    <!--@mbg.generated-->
    <!--@Table
    `dz_reconciliation_rules`-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="rule_id" jdbcType="VARCHAR" property="ruleId" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="channel_code" jdbcType="VARCHAR" property="channelCode" />
    <result column="transaction_type" jdbcType="VARCHAR" property="transactionType" />
    <result column="rule_type" jdbcType="VARCHAR" property="ruleType" />
    <result column="rule_content_json" jdbcType="LONGVARCHAR" property="ruleContentJson" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated--> `id`, `rule_id`, `version`, `channel_code`, `transaction_type`,
    `rule_type`, `rule_content_json`, `status`, `description`, `create_time`, `update_time` </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated--> update `dz_reconciliation_rules` <foreach
      close=")" collection="list" item="item" open="(" separator=", "> #{item.id,jdbcType=BIGINT} </foreach>
    where `id` in <trim prefix="set" suffixOverrides=",">
      <trim prefix="`rule_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.ruleId,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim prefix="`version` = case"
        suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.version,jdbcType=INTEGER} </foreach>
      </trim>
      <trim
        prefix="`channel_code` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.channelCode,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim
        prefix="`transaction_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.transactionType,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim
        prefix="`rule_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.ruleType,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim
        prefix="`rule_content_json` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.ruleContentJson,jdbcType=LONGVARCHAR} </foreach>
      </trim>
      <trim
        prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.status,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim prefix="`description` = case"
        suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.description,jdbcType=VARCHAR} </foreach>
      </trim>
      <trim
        prefix="`create_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.createTime,jdbcType=TIMESTAMP} </foreach>
      </trim>
      <trim
        prefix="`update_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item"> when `id` = #{item.id,jdbcType=BIGINT}
    then #{item.updateTime,jdbcType=TIMESTAMP} </foreach>
      </trim>
    </trim>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated--> update `dz_reconciliation_rules` <foreach
      close=")" collection="list" item="item" open="(" separator=", "> #{item.id,jdbcType=BIGINT} </foreach>
    where `id` in <trim prefix="set" suffixOverrides=",">
      <trim prefix="`rule_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ruleId != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.ruleId,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`version` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.version != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.version,jdbcType=INTEGER} </if>
        </foreach>
      </trim>
      <trim
        prefix="`channel_code` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.channelCode != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.channelCode,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`transaction_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.transactionType != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.transactionType,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`rule_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ruleType != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.ruleType,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`rule_content_json` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ruleContentJson != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.ruleContentJson,jdbcType=LONGVARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.status,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`description` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.description != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.description,jdbcType=VARCHAR} </if>
        </foreach>
      </trim>
      <trim
        prefix="`create_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.createTime,jdbcType=TIMESTAMP} </if>
        </foreach>
      </trim>
      <trim
        prefix="`update_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null"> when `id` = #{item.id,jdbcType=BIGINT} then
    #{item.updateTime,jdbcType=TIMESTAMP} </if>
        </foreach>
      </trim>
    </trim>
  </update>
  <update id="updateByPrimaryKeySelective"
    parameterType="com.rongchen.byh.webadmin.upms.model.DzReconciliationRulesEntity">
    <!--@mbg.generated--> update
    `dz_reconciliation_rules` <set>
      <if test="ruleId != null"> `rule_id` = #{ruleId,jdbcType=VARCHAR}, </if>
      <if
        test="version != null"> `version` = #{version,jdbcType=INTEGER}, </if>
      <if
        test="channelCode != null"> `channel_code` = #{channelCode,jdbcType=VARCHAR}, </if>
      <if
        test="transactionType != null"> `transaction_type` = #{transactionType,jdbcType=VARCHAR}, </if>
      <if
        test="ruleType != null"> `rule_type` = #{ruleType,jdbcType=VARCHAR}, </if>
      <if
        test="ruleContentJson != null"> `rule_content_json` =
    #{ruleContentJson,jdbcType=LONGVARCHAR}, </if>
      <if test="status != null"> `status` =
    #{status,jdbcType=VARCHAR}, </if>
      <if test="description != null"> `description` =
    #{description,jdbcType=VARCHAR}, </if>
      <if test="createTime != null"> `create_time` =
    #{createTime,jdbcType=TIMESTAMP}, </if>
      <if test="updateTime != null"> `update_time` =
    #{updateTime,jdbcType=TIMESTAMP}, </if>
    </set> where `id` = #{id,jdbcType=BIGINT} </update>
  <update id="updateByPrimaryKey"
    parameterType="com.rongchen.byh.webadmin.upms.model.DzReconciliationRulesEntity">
    <!--@mbg.generated--> update
    `dz_reconciliation_rules` set `rule_id` = #{ruleId,jdbcType=VARCHAR}, `version` =
    #{version,jdbcType=INTEGER}, `channel_code` = #{channelCode,jdbcType=VARCHAR},
    `transaction_type` = #{transactionType,jdbcType=VARCHAR}, `rule_type` =
    #{ruleType,jdbcType=VARCHAR}, `rule_content_json` = #{ruleContentJson,jdbcType=LONGVARCHAR},
    `status` = #{status,jdbcType=VARCHAR}, `description` = #{description,jdbcType=VARCHAR},
    `create_time` = #{createTime,jdbcType=TIMESTAMP}, `update_time` =
    #{updateTime,jdbcType=TIMESTAMP} where `id` = #{id,jdbcType=BIGINT} </update>
</mapper>