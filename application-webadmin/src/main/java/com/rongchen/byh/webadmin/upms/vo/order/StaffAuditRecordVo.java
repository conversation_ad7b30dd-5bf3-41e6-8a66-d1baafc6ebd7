package com.rongchen.byh.webadmin.upms.vo.order;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class StaffAuditRecordVo implements Serializable {

    private Long id;

    /**
     * 用户员工绑定关系表id
     */
    private Long userStaffId;

    /**
     * 复审提交时间
     */
    private Date submitTime;

    /**
     * 芝麻分截图
     */
    private String sesameImages;
    private List<String> sesameImagesList;

    /**
     * 微信截图
     */
    private String wechatImages;
    private List<String> wechatImagesList;


    /**
     * 报告截图
     */
    private String reportImages;
    private List<String> reportImagesList;

    /**
     * 审批结果 1-通过 2-拒绝
     */
    private Integer auditStatus;

    /**
     * 授信金额
     */
    private Integer creditAmount;

    /**
     * 拒绝原因
     */
    private String refuseReason;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}