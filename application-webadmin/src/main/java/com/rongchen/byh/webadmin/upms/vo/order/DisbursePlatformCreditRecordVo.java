package com.rongchen.byh.webadmin.upms.vo.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName PlatformCreditRecordVo
 * @Description 平台授信纪录
 * <AUTHOR>
 * @Date 2024/12/6 16:56
 * @Version 1.0
 **/
@Data
public class DisbursePlatformCreditRecordVo {

    @Schema(description = "授信时间")
    private String creditTime;

    @Schema(description = "授信单号")
    private String creditNo;

    @Schema(description = "授信产品")
    private String creditProductName;

    @Schema(description = "授信状态")
    private String creditStatusName;

    @Schema(description = "授信总额度")
    private String creditAmount;

    @Schema(description = "客户风险等级")
    private String riskLevelName;

    @Schema(description = "消费额度")
    private String consumeAmount;

    @Schema(description = "现金额度")
    private String cashAmount;

    @Schema(description = "经纬度")
    private String theWeft;

    @Schema(description = "经纬度地址")
    private String theWeftAddress;


}
