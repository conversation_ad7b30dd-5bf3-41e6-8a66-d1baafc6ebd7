<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.webadmin.upms.dao.SaleScheduleMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.webadmin.upms.model.SaleSchedule">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="disburseId" column="disburse_id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="repayApplyNo" column="repay_apply_no" jdbcType="VARCHAR"/>
            <result property="repayTerm" column="repay_term" jdbcType="VARCHAR"/>
            <result property="repayOwnbDate" column="repay_ownb_date" jdbcType="VARCHAR"/>
            <result property="repayOwneDate" column="repay_owne_date" jdbcType="VARCHAR"/>
            <result property="repayIntbDate" column="repay_intb_date" jdbcType="VARCHAR"/>
            <result property="repayInteDate" column="repay_inte_date" jdbcType="VARCHAR"/>
            <result property="totalAmt" column="total_amt" jdbcType="DECIMAL"/>
            <result property="termRetPrin" column="term_ret_prin" jdbcType="DECIMAL"/>
            <result property="termRetInt" column="term_ret_int" jdbcType="DECIMAL"/>
            <result property="termRetFint" column="term_ret_fint" jdbcType="DECIMAL"/>
            <result property="termStatus" column="term_status" jdbcType="VARCHAR"/>
            <result property="settleFlag" column="settle_flag" jdbcType="VARCHAR"/>
            <result property="datePay" column="date_pay" jdbcType="VARCHAR"/>
            <result property="payTime" column="pay_time" jdbcType="VARCHAR"/>
            <result property="datePayTime" column="date_pay_time" jdbcType="VARCHAR"/>
            <result property="autoRepay" column="auto_repay" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,disburse_id,user_id,
        repay_apply_no,repay_term,repay_ownb_date,
        repay_owne_date,repay_intb_date,repay_inte_date,
        total_amt,term_ret_prin,term_ret_int,
        term_ret_fint,term_status,settle_flag,pay_time
        date_pay,date_pay_time,auto_repay,create_time,
        update_time
    </sql>
    <insert id="insertBatch">
        INSERT INTO sale_schedule (
            disburse_id,user_id,
            repay_apply_no,repay_term,repay_ownb_date,
            repay_owne_date,repay_intb_date,repay_inte_date,
            total_amt,term_ret_prin,term_ret_int,
            term_ret_fint,term_status,settle_flag,
            date_pay,date_pay_time
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.disburseId}, #{item.userId}, #{item.repayApplyNo}, #{item.repayTerm}, #{item.repayOwnbDate}, #{item.repayOwneDate},
            #{item.repayIntbDate}, #{item.repayInteDate}, #{item.totalAmt}, #{item.termRetPrin}, #{item.termRetInt}, #{item.termRetFint},
            #{item.termStatus}, #{item.settleFlag}, #{item.datePay}, #{item.datePayTime})
        </foreach>
    </insert>


    <update id="updateSaleScheduleSettleFlag">
        UPDATE `sale_schedule` SET `settle_flag` = #{settleFlag},update_time = now()
        WHERE `disburse_id` = #{disburseId} and settle_flag = 'RUNNING'
    </update>
</mapper>
