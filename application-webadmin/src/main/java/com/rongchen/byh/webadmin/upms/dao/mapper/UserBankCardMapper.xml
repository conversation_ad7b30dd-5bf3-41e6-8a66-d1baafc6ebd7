<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.webadmin.upms.dao.UserBankCardMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.webadmin.upms.model.UserBankCard">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="idCard" column="id_card" jdbcType="VARCHAR"/>
            <result property="bankName" column="bank_name" jdbcType="VARCHAR"/>
            <result property="bankAccount" column="bank_account" jdbcType="VARCHAR"/>
            <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
            <result property="contractNum" column="contract_num" jdbcType="VARCHAR"/>
            <result property="customerName" column="customer_name" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="consultingMode" column="consulting_mode" jdbcType="VARCHAR"/>
            <result property="consultingRate" column="consulting_rate" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,id_card,
        bank_name,bank_account,mobile,
        contract_num,customer_name,create_time,
        update_time,consulting_mode,consulting_rate
    </sql>
    <select id="queryBackListByUserId" resultType="com.rongchen.byh.webadmin.upms.vo.bill.BackVo">
        select
            customer_name name,
            id_card idCard,
            bank_name bankName,
            bank_account bankAccount,
            contract_num contractNum,
            mobile
        from user_bank_card
        where user_id = #{userId}
    </select>
    <select id="queryBackByUserId" resultType="com.rongchen.byh.webadmin.upms.vo.bill.BackVo">
        select
            customer_name name,
            id_card idCard,
            bank_name bankName,
            bank_account bankAccount,
            mobile
        from user_bank_card
        where user_id = #{userId} order by id asc limit 1
    </select>
    <select id="queryByUserId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from user_bank_card
        where user_id = #{userId} order by id asc limit 1
    </select>
</mapper>
