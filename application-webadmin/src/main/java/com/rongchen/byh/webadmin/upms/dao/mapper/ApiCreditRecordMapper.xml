<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.webadmin.upms.dao.ApiCreditRecordMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.webadmin.upms.model.ApiCreditRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="creditNo" column="credit_no" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="outUserId" column="out_user_id" jdbcType="VARCHAR"/>
            <result property="channel" column="channel" jdbcType="VARCHAR"/>
            <result property="sourceChannel" column="source_channel" jdbcType="VARCHAR"/>
            <result property="creditStatus" column="credit_status" jdbcType="INTEGER"/>
            <result property="failReason" column="fail_reason" jdbcType="VARCHAR"/>
            <result property="creditMoney" column="credit_money" jdbcType="DECIMAL"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,credit_no,user_id,
        out_user_id,channel,source_channel,
        credit_status,fail_reason,credit_money,
        create_time,update_time
    </sql>
</mapper>
