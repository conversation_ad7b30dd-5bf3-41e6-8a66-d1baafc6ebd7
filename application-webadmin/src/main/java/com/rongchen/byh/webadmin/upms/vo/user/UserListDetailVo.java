package com.rongchen.byh.webadmin.upms.vo.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName UserDetailVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/1/23 18:23
 * @Version 1.0
 **/
@Data
public class UserListDetailVo {

    @Schema(description = "客户手机号")
    private String mobile ;

    @Schema(description = "授信状态")
    private String creditStatusName;

    @Schema(description = "是否黑名单")
    private String isBackName;

    @Schema(description = "客户姓名")
    private String userName ;

    @Schema(description = "客户性别")
    private String sex ;

    @Schema(description = "客户出生日期")
    private String birth;

    @Schema(description = "客户民族")
    private String nation;

    @Schema(description = "客户身份证号")
    private String idCard ;

    @Schema(description = "客户身份证有效期")
    private String validDate ;

    @Schema(description = "客户身份证地址")
    private String address ;

    @Schema(description = "签发机关")
    private String authority ;

    @Schema(description = "授信图片")
    private String creditUrl;

    @Schema(description = "身份证正面")
    private String idCardFrondUrl;

    @Schema(description = "身份证背面")
    private String idCardReverseUrl;

    @Schema(description = "居住状况")
    private String houseStatus;

    @Schema(description = "居住地址")
    private String houseAddress;

    @Schema(description = "单位名称")
    private String companyName ;

    @Schema(description = "单位地址")
    private String companyAddress ;

    @Schema(description = "单位电话")
    private String companyPhone ;

    @Schema(description = "公司职位")
    private String job;

    @Schema(description = "教育水平")
    private String education ;

    @Schema(description = "婚姻状况")
    private String maritalStatus;

    @Schema(description = "配偶身份证号")
    private String maritalIdNumber;

    @Schema(description = "配偶姓名")
    private String maritalName;

    @Schema(description = "配偶手机号")
    private String maritalPhone;

    @Schema(description = "配偶工作单位")
    private String maritalCompany;

    @Schema(description = "亲属联系人关系")
    private String relationshipOne;

    @Schema(description = "亲属联系人姓名")
    private String emergencyNameOne;

    @Schema(description = "亲属联系人手机号")
    private String emergencyMobileOne;

    @Schema(description = "其他联系人关系")
    private String relationshipTwo;

    @Schema(description = "其他联系人姓名")
    private String emergencyNameTwo;

    @Schema(description = "其他联系人手机号")
    private String emergencyMobileTwo;

    @Schema(description = "授信通道")
    private String creditPassage;

    @Schema(description = "所属机构")
    private String affiliatedOrganization;

    @Schema(description = "所属合作方")
    private String partner;

    @Schema(description = "授信通道信息 授信状态")
    private String creditStatusTwoName;

    @Schema(description = "授信通道信息 授信额度")
    private String creditAmount;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "总额度")
    private String totalAmount;

    @Schema(description = "可用额度")
    private String residueAmount;

    @Schema(description = "已用额度")
    private String withdrawAmount;

}
