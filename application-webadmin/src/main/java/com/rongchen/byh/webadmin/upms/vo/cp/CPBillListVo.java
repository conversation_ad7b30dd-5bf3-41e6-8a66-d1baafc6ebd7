package com.rongchen.byh.webadmin.upms.vo.cp;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 账单列表vo
 * @date 2025/1/23 10:37:01
 */
@Data
public class CPBillListVo {
    @Schema(description = "账单号")
    private Long id;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "用户手机号")
    private String mobile;

    @Schema(description = "当前期数")
    private String totalTerm;

    @Schema(description = "账单状态 已还，未还，部分还")
    private String combinedFlag;

    @Schema(description = "状态名称")
    private String creditStatusName;

    @Schema(description = "还款日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date repaymentDate;

    @Schema(description = "最新还款时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastRepayFinishTime;

    @Schema(description = "账单取消时间")
    private String cancelTime;

    @Schema(description = "应还总本金")
    private BigDecimal repayTotalPrincipalAmt;

    @Schema(description = "应还总利息")
    private BigDecimal repayTotalInterestAmt;

    @Schema(description = "已还总金额")
    private BigDecimal actualTotalAmt;

    @Schema(description = "资产方名称")
    private String providerName;

}
