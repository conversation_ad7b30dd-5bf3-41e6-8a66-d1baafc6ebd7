<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.webadmin.upms.dao.CreditOrderMapper">


    <select id="creditOrderList" resultType="com.rongchen.byh.webadmin.upms.vo.order.CreditOrderListVo">
        select distinct
            '' creditNo,
            ud.id userId,
            ude.web_name userName,
            ud.mobile mobile,
            ude.risk_level riskLevelName,
            cd.`name` userSource,
            case ud.source_mode
                when 0 then '线上'
                when 1 then '线下'
                when 2 then '空中放款'
                when 3 then '全流程API'
                else ''
            end sourceMode,
            case ul.online_type
                when 0 then '线上'
                when 1 then '线下'
                when 2 then '空中放款'
                when 3 then '全流程API'
            else ''
            end onlineType,
            '' creditProductName,
            case ul.audits_status
                when 0 then '待审核'
                when 1 then '审核通过'
                when 2 then '审核不通过'
                when 4 then '转人工'
            else '待授信'
            end creditStatusName ,
            ud.create_time createTime,
            ul.create_time creditCreateTime,
            ifnull(std.store_name,'') storeName,
            ifnull(sfd.user_name,'') saleUserName,
            ifnull(gd.group_name,'') groupName,
            (select uc.credit_amount from user_credit_data uc where ul.user_id = uc.user_id )  creditAmount
        from
            user_data ud
                left join (select web_name,risk_level,user_id from user_detail) ude on ud.id = ude.user_id
                left join channel_data cd on ud.channel_id = cd.id
                left join (select user_id , audits_status, online_type,create_time from user_loan_apply where apply_type = 0) ul on ud.id = ul.user_id
        LEFT JOIN user_staff us on us.user_id = ud.id
        LEFT JOIN staff_data sfd on sfd.id = us.staff_id
        LEFT JOIN staff_group_relation sgr on sgr.staff_id = sfd.id
        LEFT JOIN group_data gd on gd.id = sgr.group_id
        LEFT JOIN group_store_relation gsr on gsr.group_id = sgr.group_id
        LEFT JOIN store_data std on std.id = gsr.store_id
        <where>
            <if test="userName != null and userName != ''">
                and ude.web_name = #{userName}
            </if>
            <if test="mobile != null and mobile != ''">
                and ud.mobile = #{mobile}
            </if>
            <if test="creditStatus != null">
                and ul.audits_status = #{creditStatus}
            </if>
            <if test="timeStart != null and timeStart != ''">
                and ud.create_time &gt;= #{timeStart} and ud.create_time &lt;= #{timeEnd}
            </if>
            <if test="riskLevel != null and riskLevel != ''">
                and ude.risk_level = #{riskLevel}
            </if>
            <if test="channelId != null">
                and ud.channel_id = #{channelId}
            </if>
            <if test="userSource != null and userSource != ''" >
                and cd.`name` = #{userSource}
            </if>
            <if test="sysUserId != null and sysUserId != '' and sysRoleName != null and sysRoleName == '门店主管'">
                and std.sys_user_id = #{sysUserId}
            </if>
            <if test="sysUserId != null and sysUserId != '' and sysRoleName != null and sysRoleName == '门店组长'">
                and gd.sys_user_id = #{sysUserId}
            </if>
            <if test="sysUserId != null and sysUserId != '' and sysRoleName != null and sysRoleName == '门店销售'">
                and sfd.sys_user_id = #{sysUserId}
            </if>
            <if test="sysUserId != null and sysUserId != '' and sysRoleName != null and sysRoleName == '城市代理'">
                and std.sys_user_id = #{sysUserId}
            </if>
            <if test="storeName != null and storeName != ''">
                and std.store_name = #{storeName}
            </if>
            <if test="saleUserName != null and saleUserName != ''">
                and sfd.user_name = #{saleUserName}
            </if>
            <if test="groupName != null and groupName != ''">
                and gd.group_name = #{groupName}
            </if>
        </where>
        order by ud.id desc
    </select>


    <select id="userDetailVo" resultType="com.rongchen.byh.webadmin.upms.vo.order.UserDetailVo">
        select
            ud.id userId,
            ude.web_name userName,
            ude.web_id_card idNumber,
            ud.mobile mobile,
            ude.address userAddress,
            0 pointsBalance,
            '无' job,
            '无' firmName,
            '无' firmMobile,
            '无' firmAddress,
            ude.emergency_name_one familyContact,
            ude.emergency_mobile_one familyMobile,
            ude.emergency_name_two otherContact,
            ude.emergency_mobile_two otherMobile,
            '' isVip ,
            ude.id_card_frond_url idNumberCorrectUrl,
            ude.id_card_reverse_url idNumberAntiUrl,
            ude.face_url facesUrl
        from
            (select id , mobile from user_data where id = #{userId}) ud
            left join (select * from user_detail where user_id = #{userId}) ude on ud.id = ude.user_id
    </select>


    <select id="platformCreditRecord"
            resultType="com.rongchen.byh.webadmin.upms.vo.order.PlatformCreditRecordVo">
        SELECT
            ul.create_time creditTime,
            ul.id creditNo,
            case ul.audits_status
                when 0 then '待审核'
                when 1 then '审核通过'
                when 2 then '审核不通过'
                when 4 then '转人工'
            else '无'
            end creditStatusName ,
            uc.credit_amount creditAmount,
            uc.withdraw_amount consumeAmount,
            uc.residue_amount cashAmount,
            concat(ude.longitude , "," , ude.latitude) theWeft,
            ude.gps_address theWeftAddress
        FROM
            (select * from user_loan_apply where apply_type = 0 and user_id = #{userId}) ul
            left join (select * from user_credit_data where user_id = #{userId}) uc on ul.user_id = uc.user_id
            left join (select * from user_detail where user_id = #{userId}) ude on ul.user_id = ude.user_id;
    </select>


    <select id="disburseOrderList" resultType="com.rongchen.byh.webadmin.upms.vo.order.DisburseOrderResultListVo">
        select DISTINCT
            dd.id disburseId,
            ude.web_name userName,
            ud.mobile mobile,
            CONCAT(LEFT(ude.web_id_card, 6),'********',RIGHT(ude.web_id_card, 4)) IdNumber,
            ude.risk_level riskLevelName,
            case dd.credit_status
                when 100 then '授信中'
                when 200 then '授信失败'
                when 300 then '放款中'
                when 400 then '放款失败'
                when 500 then '放款成功'
                when 600 then '已结清'
                else '待授信'
                end auditStatusName ,
            '' userSource,
            '' productTypeName ,
            dd.create_time consumptionTime ,
            dd.loan_time loanTime,
            ud.create_time registerTime,
            dd.credit_amount applyAmount ,
            '' downPayment ,
            dd.periods repayTerm ,
            rs.total_amt monthlyPayment ,
            '' otherAmount,
            case ud.source_mode
                when 0 then '线上'
                when 1 then '线下'
                when 2 then '空中放款'
                when 3 then '全流程API'
                else ''
            end sourceMode,
            cd.`name` channeName,
            ifnull(std.store_name,'') storeName,
            ifnull(sfd.user_name,'') saleUserName,
            ifnull(gd.group_name,'') groupName
        FROM
            disburse_data dd
            left join user_data ud on dd.user_id = ud.id
            left join user_detail ude on ud.id = ude.user_id
            left join (select disburse_id , total_amt from repay_schedule where repay_term = 1 ) rs on dd.id = rs.disburse_id
            left join channel_data cd on ud.channel_id = cd.id
            LEFT JOIN user_staff us on us.user_id = ud.id
            LEFT JOIN staff_data sfd on sfd.id = us.staff_id
            LEFT JOIN staff_group_relation sgr on sgr.staff_id = sfd.id
            LEFT JOIN group_data gd on gd.id = sgr.group_id
            LEFT JOIN group_store_relation gsr on gsr.group_id = sgr.group_id
            LEFT JOIN store_data std on std.id = gsr.store_id
        <where>
            <if test="disburseId != null">
                and dd.id = #{disburseId}
            </if>
            <if test="userName != null and userName != ''">
                and ude.web_name = #{userName}
            </if>
            <if test="mobile != null and mobile != ''">
                and ud.mobile = #{mobile}
            </if>
            <if test="auditStatus != null">
                and dd.credit_status = #{auditStatus}
            </if>
            <if test="timeStart != null and timeStart != ''">
                and dd.create_time &gt;= #{timeStart} and dd.create_time &lt;= #{timeEnd}
            </if>
            <if test="timeLoanTimeStart != null and timeLoanTimeEnd != ''">
                and date_format(dd.loan_time,'%Y-%m-%d') &gt;= #{timeLoanTimeStart} and date_format(dd.loan_time,'%Y-%m-%d') &lt;= #{timeLoanTimeEnd}
            </if>
            <if test="timeRegisterTimeStart != null and timeRegisterTimeEnd != ''">
                and date_format(ud.create_time,'%Y-%m-%d') &gt;= #{timeRegisterTimeStart} and date_format(ud.create_time,'%Y-%m-%d') &lt;= #{timeRegisterTimeEnd}
            </if>
            <if test="riskLevel != null and riskLevel != ''">
                and ude.risk_level = #{riskLevel}
            </if>
            <if test="idNumber != null and idNumber != ''">
                and ude.web_id_card = #{idNumber}
            </if>
            <if test="channelId != null and channelId != ''">
                and cd.id IN
                <foreach item="item" index="index" collection="channelId.split(',')"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="channeName != null and channeName != ''">
                and cd.name = #{channeName}
            </if>
            <if test="storeName != null and storeName != ''">
                and std.store_name = #{storeName}
            </if>
            <if test="saleUserName != null and saleUserName != ''">
                and sfd.user_name = #{saleUserName}
            </if>
            <if test="groupName != null and groupName != ''">
                and gd.group_name = #{groupName}
            </if>
            <if test="sysUserId != null and sysUserId != '' and sysRoleName != null and sysRoleName == '门店主管'">
                and std.sys_user_id = #{sysUserId}
            </if>
            <if test="sysUserId != null and sysUserId != '' and sysRoleName != null and sysRoleName == '门店组长'">
                and gd.sys_user_id = #{sysUserId}
            </if>
            <if test="sysUserId != null and sysUserId != '' and sysRoleName != null and sysRoleName == '门店销售'">
                and sfd.sys_user_id = #{sysUserId}
            </if>
            <if test="sysUserId != null and sysUserId != '' and sysRoleName != null and sysRoleName == '城市代理'">
                and std.sys_user_id = #{sysUserId}
            </if>
        </where>
        order by  dd.create_time desc
    </select>


    <select id="getUserIdByDisburseId" resultType="java.lang.Long">
        select user_id from disburse_data where id = #{disburseId}
    </select>


    <select id="disburseListDetail"
            resultType="com.rongchen.byh.webadmin.upms.vo.order.DisburseOrderListDetailVo">
        select distinct
            ud.id userId,
            ude.web_name userName,
            CONCAT(LEFT(ude.web_id_card, 6),'********',RIGHT(ude.web_id_card, 4)) IdNumber,
            ud.mobile mobile,
            ude.address userAddress,
            0 pointsBalance,
            '无' job,
            '无' firmName,
            '无' firmMobile,
            '无' firmAddress,
            ude.emergency_name_one familyContact,
            ude.emergency_mobile_one familyMobile,
            ude.emergency_name_two otherContact,
            ude.emergency_mobile_two otherMobile,
            '' isVip ,
            ude.create_time userCreateTime,
            cd.`name` channelName,
            case ud.source_mode
                when 0 then '线上'
                when 1 then '线下'
                when 2 then '空中放款'
                when 3 then '全流程API'
                else ''
            end sourceMode,

-- 	订单信息
            dd.loan_no,
            dd.credit_no,
            dd.id disburseId,
            case dd.fund_code
                when 'XJ_HB2' then '湖北消金'
                when 'XJ_WP' then '唯品消金'
                when 'XJ_ZY_DY' then '中原东营联合贷'
                when 'YH_HY_HE' then '韩亚银行'
                when 'YH_MS' then '蒙商消金'
                when 'YH_ZX' then '振兴银行'
                when 'YH_LH_HE' then '海尔蓝海银行'
                else dd.fund_code
                end fundCode,
            '' productTypeName,
            case dd.credit_status
                when 100 then '授信中'
                when 200 then '授信失败'
                when 300 then '放款中'
                when 400 then '放款失败'
                when 500 then '还款中'
                when 600 then '已结清'
            else ''
            end disburseStatusName ,
            dd.create_time disburseCreateTime,
            0 commodityNum,
            '' commodityMessage,
            '' commodityPrice,
        '' consigneeName,
        '' consigneeMobile,
        '未知' isConsignee,
        dd.credit_amount installmentAmount,
        dd.year_rete yearRate,
        ubc.bank_name backName,
        concat('****** **** ****' , right(ubc.bank_account , 4)) backAccount,
        IF(ubc.mobile = ud.mobile ,'一致','不一致') isIdentical,
        '' otherAmount,
        '' amountRate,
        '' otherAmountType,
        ude.id_card_frond_url idNumberCorrectUrl,
        ude.id_card_reverse_url idNumberAntiUrl,
        ude.face_url facesUrl
        from
            (select * from disburse_data where id = #{disburseId} ) dd
            left join (select id , mobile , channel_id, source_mode  from user_data where id = #{userId}) ud on dd.user_id = ud.id
            left join (select * from user_detail where user_id = #{userId}) ude on ud.id = ude.user_id
            left join (select * from user_bank_card where user_id = #{userId}) ubc on ud.id = ubc.user_id
            left join (select * from user_loan_apply where apply_type = 0 and user_id = #{userId}) ul on ud.id = ul.user_id
            left join channel_data cd on ud.channel_id = cd.id
    </select>


    <select id="userList" resultType="com.rongchen.byh.webadmin.upms.vo.user.UserListVo">
        SELECT
            ud.id id,
            ude.web_name userName,
            ud.mobile mobile,
            cd.`name` channelName,
            case ud.source_mode
                when 0 then '线上'
                when 1 then '线下'
                when 2 then '空中放款'
                when 3 then '全流程API'
                else ''
            end sourceMode,
            ud.status_flag statusFlag,
            if(ud.status_flag = 1 , '正常' , '禁用') statusFlagName,
        if((ude.ocr_result = 0 or ude.form_flag is null) , '待实名',if(ude.form_flag = 0, '待补充基础资料' , '已授信')) creditStatusName ,
            '否' isBack ,
            ud.create_time createTime
        FROM
            user_data ud
            left join (select * from user_loan_apply where apply_type = 0) ul  on ul.user_id = ud.id
            left join channel_data cd on cd.id = ud.channel_id
            left join user_detail ude on ud.id = ude.user_id
        <where>
            <if test="channelId != null">
                ud.channel_id = #{channelId}
            </if>
            <if test="userName != null and userName != ''">
                and ude.web_name = #{userName}
            </if>
            <if test="mobile != null and mobile != ''">
                and ud.mobile = #{mobile}
            </if>
            <if test="channelName != null and channelName != ''">
                and cd.name = #{channelName}
            </if>
            <if test="statusFlag != null and statusFlag != ''">
               and ud.status_flag = #{statusFlag}
            </if>
            <if test="creditsStatus != null and creditsStatus == 1">
                and ude.ocr_result = 0
            </if>
            <if test="creditsStatus != null and creditsStatus == 2">
                and ude.form_flag = 0
            </if>
            <if test="creditsStatus != null and creditsStatus == 3">
                and ude.form_flag = 1
            </if>
            <if test="timeStart != null and timeStart != ''">
                and ud.create_time &gt;= #{timeStart} and ud.create_time &lt;= #{timeEnd}
            </if>
            <if test="channelId != null and channelId != ''">
                and ud.channel_id IN
                <foreach item="item" index="index" collection="channelId.split(',')"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by  ud.create_time desc
    </select>
    <select id="disbursePlatformCreditRecord"
            resultType="com.rongchen.byh.webadmin.upms.vo.order.DisbursePlatformCreditRecordVo">
        SELECT
            ul.create_time creditTime,
            ul.id creditNo,
            '' creditProductName,
            case ul.audits_status
                when 0 then '待审核'
                when 1 then '审核通过'
                when 2 then '审核不通过'
                when 4 then '转人工'
                else '待授信'
                end creditStatusName ,
            uc.credit_amount creditAmount,
            ude.risk_level riskLevelName,
            uc.withdraw_amount consumeAmount,
            uc.residue_amount cashAmount,
            concat(ude.longitude , "," , ude.latitude) theWeft,
            ude.gps_address theWeftAddress
        FROM
            (select * from user_loan_apply where apply_type = 0 and user_id = #{userId}) ul
            left join (select * from user_credit_data where user_id = #{userId}) uc on ul.user_id = uc.user_id
            left join (select * from user_detail where user_id = #{userId}) ude on ul.user_id = ude.user_id;
    </select>
    <select id="historicalRepayment"
            resultType="com.rongchen.byh.webadmin.upms.vo.order.HistoricalRepaymentVo">
        select
            dd.user_id userId,
            dd.id disburseId,
            '' productName,
            rsc.sum repaymentTotalAmount,
            rsc.num repaymentTotalRepay,
            rsc1.num oughtRepay,
            rsc1.sum oughtRepayAmount,
            rsco.datex overDayNum,
            rscm.datex overDayNumMax,
            rscn.num historicalOverNum,
            '' collectionAbstract
        from
            disburse_data dd
                left join (select disburse_id , count(1) num , sum(total_amt) sum from repay_schedule where settle_flag = 'CLOSE' ) rsc on dd.id = rsc.disburse_id
                left join (select disburse_id , count(1) num , sum(total_amt) sum from repay_schedule where settle_flag != 'CLOSE' ) rsc1 on dd.id = rsc1.disburse_id
                left join (select disburse_id , max(datediff(date(now()) , repay_owne_date)) datex from repay_schedule where term_status = 'O' and settle_flag != 'CLOSE' GROUP BY disburse_id) rsco on dd.id = rsco.disburse_id
                left join (select disburse_id , max(datediff(IFNULL(date_pay , date(now())) , repay_owne_date)) datex from repay_schedule where term_status = 'O'  GROUP BY disburse_id) rscm on dd.id = rscm.disburse_id
                left join (select disburse_id , count(1) num from repay_schedule where term_status = 'O' GROUP BY disburse_id) rscn on dd.id = rscn.disburse_id
    </select>

    <select id="queryUserStaff" resultType="com.rongchen.byh.webadmin.upms.vo.order.UserStaffVo">
        select * from user_staff where user_id = #{userId}
    </select>

    <select id="queryStaffAuditRecord" resultType="com.rongchen.byh.webadmin.upms.vo.order.StaffRecordVo">
        select  sd.user_name userName ,
                sd.mobile,
                sar.audit_status as auditStatus,
                sar.credit_amount as creditAmount,
                sar.refuse_reason as refuseReason
        from user_staff us
        LEFT JOIN staff_data sd ON sd.id = us.staff_id
        LEFT JOIN staff_audit_record sar ON sd.id = sar.user_staff_id
        where us.user_id = #{userId}
    </select>

    <select id="staffAuditRecord" resultType="com.rongchen.byh.webadmin.upms.vo.order.StaffAuditRecordVo">
        select * from staff_audit_record where user_staff_id = #{staffId};
    </select>


</mapper>
