package com.rongchen.byh.webadmin.upms.vo.order;

import com.rongchen.byh.webadmin.upms.model.UserLoveLog;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 支用订单详情vo
 * @date 2025/1/23 11:19:25
 */
@Data
public class DisburseOrderDetailVo {
    @Schema(description = "还款单号")
    private Long id;

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "授信单号")
    private String creditOrderId;

    @Schema(description = "机构名称")
    private String institutionName;

    @Schema(description = "合作方名称")
    private String userChannelName;

    @Schema(description = "用户名称")
    private String userName;

    @Schema(description = "用户手机号")
    private String mobile;

    @Schema(description = "状态")
    private Integer creditStatus;

    @Schema(description = "状态值")
    private String creditStatusName;

    @Schema(description = "阶段状态")
    private Integer stageStatus;

    @Schema(description = "阶段状态值")
    private String stageStatusName;

    @Schema(description = "渠道名")
    private String channelName;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "申请金额")
    private BigDecimal creditAmount;

    @Schema(description = "申请用途")
    private String purposeLoan;

    @Schema(description = "还款方式")
    private String repaymentMethod;

    @Schema(description = "年利率")
    private String yearRete;

    @Schema(description = "期数单位")
    private String periodsUnit;

    @Schema(description = "期数单位值")
    private String periodsUnitValue;

    @Schema(description = "期数")
    private Integer periods;

    @Schema(description = "银行信息")
    private String bankName;

    @Schema(description = "信审结果")
    private String creditResult;

    @Schema(description = "其他费用")
    private String otherFee;

    @Schema(description = "其他费用利率")
    private String otherFeeRate;

    @Schema(description = "其他费用类型")
    private String otherFeeType;

    @Schema(description = "放款金额")
    private BigDecimal loanAmount;

    @Schema(description = "到款金额")
    private BigDecimal arrivalAmount;

    @Schema(description = "放款申请时间")
    private String loanApplyTime;

    @Schema(description = "放款时间")
    private String loanTime;

    @Schema(description = "放款申请单号")
    private String loanApplyNo;

    @Schema(description = "渠道商户号")
    private String channelMerchantNo;

    @Schema(description = "渠道应答单号")
    private String channelResponseNo;

    @Schema(description = "渠道放款支付单号")
    private String channelLoanPayNo;

    @Schema(description = "渠道放款完成时间")
    private String channelLoanFinishTime;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "登账时间")
    private String accountTime;

    @Schema(description = "是否创建账单")
    private String isCreateBill;

    @Schema(description = "账单创建时间")
    private String billCreateTime;

    @Schema(description = "通知书编号")
    private String noticeNo;

    @Schema(description = "债转通知书URL")
    private String debtNoticeUrl;

    @Schema(description = "债转对价")
    private BigDecimal debtPrice;

    @Schema(description = "债转协议编号")
    private String debtAgreementNo;

    @Schema(description = "资方名称")
    private String capitalName;

    @Schema(description = "资方订单号")
    private String contractId;

    @Schema(description = "合同信息")
    private List<UserLoveLog> userLoveLogList;

}
