<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.webadmin.upms.dao.StaffDataMapper">

    <resultMap type="com.rongchen.byh.webadmin.upms.model.StaffData" id="StaffDataMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userName" column="user_name" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="inviteCode" column="invite_code" jdbcType="VARCHAR"/>
        <result property="inviteFlag" column="invite_flag" jdbcType="INTEGER"/>
        <result property="staffStatus" column="staff_status" jdbcType="INTEGER"/>
        <result property="sysUserId" column="sys_user_id" jdbcType="BIGINT"/>
        <result property="showName" column="show_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!--查询数据-->
    <select id="queryAll" resultMap="StaffDataMap">
        select id,
               user_name,
               mobile,
               invite_code,
               invite_flag,
               staff_status,
               create_time,
               update_time
        from staff_data
    </select>

    <!--查询指定行数据-->
    <select id="staffDataList" resultMap="StaffDataMap">
        select
            sd.id,
            sd.user_name,
            sd.mobile,
            sd.invite_code,
            sd.sys_user_id ,
            sd.invite_flag,
            sd.staff_status,
            sd.create_time,
            sd.update_time,
            su.show_name
        from staff_data sd
        LEFT JOIN sys_user su on sd.sys_user_id = su.user_id
        <where>
            <if test="userName != null and userName != ''">
                and sd.user_name = #{userName}
            </if>
            <if test="mobile != null and mobile != ''">
                and sd.mobile = #{mobile}
            </if>
            <if test="inviteCode != null and inviteCode != ''">
                and sd.invite_code = #{inviteCode}
            </if>
            <if test="inviteFlag != null">
                and sd.invite_flag = #{inviteFlag}
            </if>
            <if test="staffStatus != null">
                and sd.staff_status = #{staffStatus}
            </if>
            <if test="showName != null and showName != ''">
                and su.show_name = #{showName}
            </if>
        </where>
        order by create_time desc
    </select>

    <update id="ordersStaff">
        update user_staff set staff_id = #{staffId} where staff_id = #{oldStaffId}
    </update>

    <select id="selectStaffList" resultType="com.rongchen.byh.webadmin.upms.vo.order.UserStaffVo">
        select * from user_staff where staff_id = #{staffId}
    </select>
</mapper>

