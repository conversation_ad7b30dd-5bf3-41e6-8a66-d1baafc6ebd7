package com.rongchen.byh.webadmin.upms.vo.bill;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 账单详情vo
 * @date 2025/1/23 11:19:01
 */
@Data
public class BillDetailVo {
    @Schema(description = "账单号")
    private Long id;

    @Schema(description = "回收申请单ID")
    private Long recycleOrderId;

    @Schema(description = "状态码")
    private Integer creditStatus;

    @Schema(description = "状态名称")
    private String creditStatusName;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "用户手机号")
    private String mobile;

    @Schema(description = "用户身份证")
    private String idCard;

    @Schema(description = "用户风险等级")
    private String riskLevel;

    @Schema(description = "开户行")
    private String bankName;

    @Schema(description = "银行卡号")
    private String bankAccount;

    @Schema(description = "还款通道名称")
    private String repayChannelName;

    @Schema(description = "还款日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date repaymentDate;

    @Schema(description = "还款方式")
    private String repaymentMethod;

    @Schema(description = "年利率")
    private String yearRete;

    @Schema(description = "期数单位")
    private String periodsUnit;

    @Schema(description = "期数")
    private Integer periods;

    @Schema(description = "应还总金额")
    private BigDecimal repayTotalAmt;

    @Schema(description = "已还总金额")
    private BigDecimal actualTotalAmt;

    @Schema(description = "应还总本金")
    private BigDecimal repayTotalPrincipalAmt;

    @Schema(description = "已还总本金")
    private BigDecimal actualTotalPrincipalAmt;

    @Schema(description = "应还总利息")
    private BigDecimal repayTotalInterestAmt;

    @Schema(description = "已还总利息")
    private BigDecimal actualTotalInterestAmt;

    @Schema(description = "应还总罚息")
    private BigDecimal repayTotalPenaltyAmt;

    @Schema(description = "已还总罚息")
    private BigDecimal actualTotalPenaltyAmt;

    @Schema(description = "应还其他费用")
    private BigDecimal repayOtherAmt;

    @Schema(description = "已还其他费用")
    private BigDecimal actualOtherAmt;

    @Schema(description = "放款资金方")
    private String  fundCode;

    @Schema(description = "紧急联系人关系1")
    private String  relationshipOne;

    @Schema(description = "紧急联系人姓名1")
    private String  emergencyNameOne;

    @Schema(description = "紧急联系人手机号1")
    private String  emergencyMobileOne;

    @Schema(description = "紧急联系人关系2")
    private String  relationshipTwo;

    @Schema(description = "紧急联系人姓名2")
    private String  emergencyNameTwo;

    @Schema(description = "紧急联系人手机号3")
    private String  emergencyMobileTwo;

    @Schema(description = "api渠道")
    private String  apiChannel;

    @Schema(description = "账单期数信息")
    List<BillPeriodDetailVo> billPeriodDetailVoList;
}
