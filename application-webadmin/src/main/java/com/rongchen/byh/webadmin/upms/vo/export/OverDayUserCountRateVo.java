package com.rongchen.byh.webadmin.upms.vo.export;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class OverDayUserCountRateVo {
    @Schema(description = "放款日期", example = "2023-04-15")
    private String loanDate;

    @Schema(description = "渠道号", example = "xhy")
    private String channelName;

    @Schema(description = "回收率", example = "2.00%")
    private String overdue;

    @Schema(description = "逾期1天内率", example = "5.00%")
    private String overDay1Rate;

    @Schema(description = "逾期2天内率", example = "4.50%")
    private String overDay2Rate;

    @Schema(description = "逾期3天内率", example = "4.00%")
    private String overDay3Rate;

    @Schema(description = "逾期4天内率", example = "3.50%")
    private String overDay4Rate;

    @Schema(description = "逾期5天内率", example = "3.00%")
    private String overDay5Rate;

    @Schema(description = "逾期6天内率", example = "2.50%")
    private String overDay6Rate;

    @Schema(description = "逾期7天内率", example = "2.00%")
    private String overDay7Rate;

    @Schema(description = "逾期8天内率", example = "1.50%")
    private String overDay8Rate;

    @Schema(description = "逾期9天内率", example = "1.00%")
    private String overDay9Rate;

    @Schema(description = "逾期10天内率", example = "0.80%")
    private String overDay10Rate;

    @Schema(description = "逾期11天内率", example = "0.60%")
    private String overDay11Rate;

    @Schema(description = "逾期12天内率", example = "0.40%")
    private String overDay12Rate;

    @Schema(description = "逾期13天内率", example = "0.20%")
    private String overDay13Rate;

    @Schema(description = "逾期14天内率", example = "0.10%")
    private String overDay14Rate;

    @Schema(description = "逾期15天内率", example = "0.05%")
    private String overDay15Rate;
}
