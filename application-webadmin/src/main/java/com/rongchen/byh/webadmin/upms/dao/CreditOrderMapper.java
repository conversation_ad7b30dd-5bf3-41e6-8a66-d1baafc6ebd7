package com.rongchen.byh.webadmin.upms.dao;


import com.rongchen.byh.webadmin.upms.dto.order.*;
import com.rongchen.byh.webadmin.upms.dto.user.UserListDto;
import com.rongchen.byh.webadmin.upms.vo.order.*;
import com.rongchen.byh.webadmin.upms.vo.user.UserListVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 授信列表
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Mapper
public interface CreditOrderMapper {

    List<CreditOrderListVo> creditOrderList(CreditOrderListDto dto);

    UserDetailVo userDetailVo(CreditOrderListDetailDto dto);

    List<PlatformCreditRecordVo> platformCreditRecord(CreditOrderListDetailDto dto);

    List<DisburseOrderResultListVo> disburseOrderList(DisburseOrderRequestListDto dto);

    Long getUserIdByDisburseId(Long disburseId);

    DisburseOrderListDetailVo disburseListDetail(DisburseOrderListDetailDto dto);

    List<UserListVo> userList(UserListDto dto);

    List<DisbursePlatformCreditRecordVo> disbursePlatformCreditRecord(DisburseOrderListDetailDto dto);

    List<HistoricalRepaymentVo> historicalRepayment(DisburseOrderListDetailDto dto);

    UserStaffVo queryUserStaff(String userId);

    List<StaffRecordVo> queryStaffAuditRecord(String userId);

    StaffAuditRecordVo staffAuditRecord(Long staffId);
}
