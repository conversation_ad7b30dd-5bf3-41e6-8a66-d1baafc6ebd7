package com.rongchen.byh.webadmin.upms.vo.export;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class OverDayUserAmountRateVo {
    @Schema(description = "放款日期", example = "2023-04-15")
    private String loanDate;

    @Schema(description = "渠道号", example = "xhy")
    private String channelName;

    @Schema(description = "回收率", example = "2.00%")
    private String overdue;

    @Schema(description = "逾期1天内金额率", example = "2.50%")
    private String overdue1DayAmountRate;

    @Schema(description = "逾期2天内金额率", example = "2.00%")
    private String overdue2DayAmountRate;

    @Schema(description = "逾期3天内金额率", example = "1.80%")
    private String overdue3DayAmountRate;

    @Schema(description = "逾期4天内金额率", example = "1.50%")
    private String overdue4DayAmountRate;

    @Schema(description = "逾期5天内金额率", example = "1.20%")
    private String overdue5DayAmountRate;

    @Schema(description = "逾期6天内金额率", example = "1.00%")
    private String overdue6DayAmountRate;

    @Schema(description = "逾期7天内金额率", example = "0.80%")
    private String overdue7DayAmountRate;

    @Schema(description = "逾期8天内金额率", example = "0.60%")
    private String overdue8DayAmountRate;

    @Schema(description = "逾期9天内金额率", example = "0.50%")
    private String overdue9DayAmountRate;

    @Schema(description = "逾期10天内金额率", example = "0.40%")
    private String overdue10DayAmountRate;

    @Schema(description = "逾期11天内金额率", example = "0.30%")
    private String overdue11DayAmountRate;

    @Schema(description = "逾期12天内金额率", example = "0.25%")
    private String overdue12DayAmountRate;

    @Schema(description = "逾期13天内金额率", example = "0.20%")
    private String overdue13DayAmountRate;

    @Schema(description = "逾期14天内金额率", example = "0.15%")
    private String overdue14DayAmountRate;

    @Schema(description = "逾期15天内金额率", example = "0.10%")
    private String overdue15DayAmountRate;
}
