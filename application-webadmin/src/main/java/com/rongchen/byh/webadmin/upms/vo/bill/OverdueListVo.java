package com.rongchen.byh.webadmin.upms.vo.bill;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 逾期账单列表vo
 * @date 2025/3/18 10:37:01
 */
@Data
public class OverdueListVo {
    @Schema(description = "账单号")
    private Long id;

    @Schema(description = "客户渠道")
    private String userChannelName;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "用户手机号")
    private String mobile;

    @Schema(description = "用户风险等级")
    private String riskLevel;

    @Schema(description = "状态编码")
    private Integer creditStatus;

    @Schema(description = "状态名称")
    private String creditStatusName;

    @Schema(description = "本息状态")
    private String termStatus;

    @Schema(description = "期数")
    private String  totalTerm;

    @Schema(description = "产品名称")
    private String productName;

    @Schema(description = "还款日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date repaymentDate;

    @Schema(description = "账单取消时间")
    private String cancelTime;

    @Schema(description = "应还总本金")
    private BigDecimal repayTotalPrincipalAmt;

    @Schema(description = "应还总利息")
    private BigDecimal repayTotalInterestAmt;

    @Schema(description = "应还总金额")
    private BigDecimal repayTotalAmt;

    @Schema(description = "已还总金额")
    private BigDecimal actualTotalAmt;

    @Schema(description = "应还其他费用")
    private BigDecimal repayOtherAmt;

    @Schema(description = "已还其他费用")
    private BigDecimal actualOtherAmt;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "门店名称")
    private String  storeName;

    @Schema(description = "销售员名称")
    private String  saleUserName;

    @Schema(description = "小组名称")
    private String  groupName;

    @Schema(description = "逾期天数")
    private String overdueDays;

    @Schema(description = "权益状态")
    private String saleScheduleStatus;

    @Schema(description = "账单状态")
    private String combinedFlag;

    @Schema(description = "逾期金额")
    private String totalRemainingAmountDue;

    @Schema(description = "来源模式")
    private String sourceMode;


}
