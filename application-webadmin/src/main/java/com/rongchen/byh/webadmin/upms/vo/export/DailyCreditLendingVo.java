package com.rongchen.byh.webadmin.upms.vo.export;

import lombok.Data;

@Data
public class DailyCreditLendingVo {
    // 基础维度字段
    private String createTime;         // 日期（格式：yyyy-MM-dd）
    private String channelName;        // 渠道名称（对应channel_data.name）
    private String sourceMode;         // 来源模式（线上/线下/空中放款/全流程API-xhy）
    private String channelId;         // 渠道id

    // 注册用户数（selectCountZC）
    private Integer registrationUser;  // 注册用户数（count字段）

    // 进件客户数（selectCountJJ）
    private Integer applicationCustomer; // 进件客户数（count字段）

    // 授信通过客户数（selectCountSXTG）
    private Integer approvedCustomer;  // 授信通过客户数（count字段）

    // 授信失败客户数（selectCountSXJJ）
    private Integer failedCustomer;    // 授信失败客户数（count字段）

    // 放款相关字段（selectCountPay）
    private Integer payoutUser;        // 提现用户数（payoutsUser字段）(放款成功，放款失败)
    private Integer successUser;       // 放款成功用户数（successUser字段）
    private Integer errorUser;         // 放款失败用户数（errorUser字段）
    private Double successAmount;      // 放款成功金额（successAmount字段）

    // 计算字段（由Java代码填充，非Mapper直接返回）
    private String approvalRate;       // 授信通过率（格式：XX.XX%）
    private String averageAmount;      // 件均金额（格式：XX.XX）
    private String payoutSuccessRate;  // 放款成功率（格式：XX.XX%）

}
