package com.rongchen.byh.webadmin.upms.vo.cp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName DisburseOrderListVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/1/23 11:49
 * @Version 1.0
 **/
@Data
public class CPDisburseDataResultListVo {

    @Schema(description = "订单号")
    private Long disburseId;

    @Schema(description = "客户名称")
    private String userName;

    @Schema(description = "客户手机号")
    private String mobile;

    @Schema(description = "客户身份证")
    private String IdNumber;

    @Schema(description = "放款状态")
    private String auditStatusName;

    @Schema(description = "支用时间")
    private String consumptionTime;

    @Schema(description = "申请金额")
    private String applyAmount;

    @Schema(description = "期数")
    private Integer repayTerm;

    @Schema(description = "放款成功时间")
    private String loanTime;

    @Schema(description = "资产方名称")
    private String providerName;

}
