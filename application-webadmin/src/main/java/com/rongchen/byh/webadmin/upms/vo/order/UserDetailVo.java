package com.rongchen.byh.webadmin.upms.vo.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @ClassName UserDetailVo
 * @Description 客户信息
 * <AUTHOR>
 * @Date 2024/12/6 16:17
 * @Version 1.0
 **/
@Data
public class UserDetailVo {

    @Schema(description = "客户id")
    private String userId;

    @Schema(description = "客户名称")
    private String userName;

    @Schema(description = "客户身份证")
    private String idNumber;

    @Schema(description = "注册手机号")
    private String mobile;

    @Schema(description = "客户户籍地址")
    private String userAddress;

    @Schema(description = "积分余额")
    private String pointsBalance;

    @Schema(description = "职业")
    private String job;

    @Schema(description = "公司名称")
    private String firmName;

    @Schema(description = "公司电话")
    private String firmMobile;

    @Schema(description = "公司地址")
    private String firmAddress;

    @Schema(description = "家庭联系人")
    private String familyContact;

    @Schema(description = "家庭联系人号码")
    private String familyMobile;

    @Schema(description = "其他联系人")
    private String otherContact;

    @Schema(description = "其他联系人号码")
    private String otherMobile;

    @Schema(description = "会员")
    private String isVip;

    @Schema(description = "销售人员姓名")
    private String userNameStaff;

    @Schema(description = "销售人员手机号")
    private String mobileStaff;

    @Schema(description = "审批结果 1-通过 2-拒绝")
    private Integer auditStatus;

    @Schema(description = "授信金额")
    private Integer creditAmount;

    @Schema(description = "拒绝原因")
    private String refuseReason;

    @Schema(description = "审核时间")
    private Date auditTime;

    @Schema(description = "通道授信记录")
    private List<PassageCreditRecordVo> creditRecordList;

    @Schema(description = "平台授信记录")
    private List<PlatformCreditRecordVo> platformCreditRecordList;

    @Schema(description = "身份证正面")
    private String idNumberCorrectUrl;

    @Schema(description = "身份证反面")
    private String idNumberAntiUrl;

    @Schema(description = "人脸图像")
    private String facesUrl;

    @Schema(description = "通信录信息")
    private List<ContactsVo> contactsList;

    @Schema(description = "探针征信源")
    private Object probe;

    @Schema(description = "雷达征信源")
    private Object radar;

    @Schema(description = "面核资料")
    private StaffAuditRecordVo staffAuditRecordVo;
}
