<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.webadmin.upms.dao.DzRawPartnerCreditDataMapper">
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.rongchen.byh.webadmin.upms.model.DzRawPartnerCreditDataEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `dz_raw_partner_credit_data`
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="syncBatchId != null">
        `sync_batch_id`,
      </if>
      <if test="channelCode != null">
        `channel_code`,
      </if>
      <if test="processingDate != null">
        `processing_date`,
      </if>
      <if test="transactionType != null">
        `transaction_type`,
      </if>
      <if test="originalFilename != null">
        `original_filename`,
      </if>
      <if test="partnerCreditNo != null">
        `partner_credit_no`,
      </if>
      <if test="creditDate != null">
        `credit_date`,
      </if>
      <if test="creditAmount != null">
        `credit_amount`,
      </if>
      <if test="creditStatus != null">
        `credit_status`,
      </if>
      <if test="extendedFieldsJson != null">
        `extended_fields_json`,
      </if>
      <if test="loadTime != null">
        `load_time`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="syncBatchId != null">
        #{syncBatchId,jdbcType=VARCHAR},
      </if>
      <if test="channelCode != null">
        #{channelCode,jdbcType=VARCHAR},
      </if>
      <if test="processingDate != null">
        #{processingDate,jdbcType=DATE},
      </if>
      <if test="transactionType != null">
        #{transactionType,jdbcType=VARCHAR},
      </if>
      <if test="originalFilename != null">
        #{originalFilename,jdbcType=VARCHAR},
      </if>
      <if test="partnerCreditNo != null">
        #{partnerCreditNo,jdbcType=VARCHAR},
      </if>
      <if test="creditDate != null">
        #{creditDate,jdbcType=DATE},
      </if>
      <if test="creditAmount != null">
        #{creditAmount,jdbcType=DECIMAL},
      </if>
      <if test="creditStatus != null">
        #{creditStatus,jdbcType=VARCHAR},
      </if>
      <if test="extendedFieldsJson != null">
        #{extendedFieldsJson,jdbcType=VARCHAR},
      </if>
      <if test="loadTime != null">
        #{loadTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `dz_raw_partner_credit_data`
    (`sync_batch_id`, `channel_code`, `processing_date`, `transaction_type`, `original_filename`,
      `partner_credit_no`, `credit_date`, `credit_amount`, `credit_status`, `extended_fields_json`,
      `load_time`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.syncBatchId,jdbcType=VARCHAR}, #{item.channelCode,jdbcType=VARCHAR}, #{item.processingDate,jdbcType=DATE},
        #{item.transactionType,jdbcType=VARCHAR}, #{item.originalFilename,jdbcType=VARCHAR},
        #{item.partnerCreditNo,jdbcType=VARCHAR}, #{item.creditDate,jdbcType=DATE}, #{item.creditAmount,jdbcType=DECIMAL},
        #{item.creditStatus,jdbcType=VARCHAR}, #{item.extendedFieldsJson,jdbcType=VARCHAR},
        #{item.loadTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="batchInsertOrUpdate" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into `dz_raw_partner_credit_data`
    (`sync_batch_id`, `channel_code`, `processing_date`, `transaction_type`, `original_filename`,
      `partner_credit_no`, `credit_date`, `credit_amount`, `credit_status`, `extended_fields_json`,
      `load_time`)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.syncBatchId,jdbcType=VARCHAR}, #{item.channelCode,jdbcType=VARCHAR}, #{item.processingDate,jdbcType=DATE},
        #{item.transactionType,jdbcType=VARCHAR}, #{item.originalFilename,jdbcType=VARCHAR},
        #{item.partnerCreditNo,jdbcType=VARCHAR}, #{item.creditDate,jdbcType=DATE}, #{item.creditAmount,jdbcType=DECIMAL},
        #{item.creditStatus,jdbcType=VARCHAR}, #{item.extendedFieldsJson,jdbcType=VARCHAR},
        #{item.loadTime,jdbcType=TIMESTAMP})
    </foreach>
    on duplicate key update
    sync_batch_id=values(sync_batch_id),
    channel_code=values(channel_code),
    processing_date=values(processing_date),
    transaction_type=values(transaction_type),
    original_filename=values(original_filename),
    partner_credit_no=values(partner_credit_no),
    credit_date=values(credit_date),
    credit_amount=values(credit_amount),
    credit_status=values(credit_status),
    extended_fields_json=values(extended_fields_json),
    load_time=values(load_time)
  </insert>
  <resultMap id="BaseResultMap" type="com.rongchen.byh.webadmin.upms.model.DzRawPartnerCreditDataEntity">
    <!--@mbg.generated-->
    <!--@Table `dz_raw_partner_credit_data`-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sync_batch_id" jdbcType="VARCHAR" property="syncBatchId" />
    <result column="channel_code" jdbcType="VARCHAR" property="channelCode" />
    <result column="processing_date" jdbcType="DATE" property="processingDate" />
    <result column="transaction_type" jdbcType="VARCHAR" property="transactionType" />
    <result column="original_filename" jdbcType="VARCHAR" property="originalFilename" />
    <result column="partner_credit_no" jdbcType="VARCHAR" property="partnerCreditNo" />
    <result column="credit_date" jdbcType="DATE" property="creditDate" />
    <result column="credit_amount" jdbcType="DECIMAL" property="creditAmount" />
    <result column="credit_status" jdbcType="VARCHAR" property="creditStatus" />
    <result column="extended_fields_json" jdbcType="VARCHAR" property="extendedFieldsJson" />
    <result column="load_time" jdbcType="TIMESTAMP" property="loadTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    `id`, `sync_batch_id`, `channel_code`, `processing_date`, `transaction_type`, `original_filename`,
    `partner_credit_no`, `credit_date`, `credit_amount`, `credit_status`, `extended_fields_json`,
    `load_time`
  </sql>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `dz_raw_partner_credit_data`
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
    where `id` in
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`sync_batch_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.syncBatchId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`channel_code` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.channelCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`processing_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.processingDate,jdbcType=DATE}
        </foreach>
      </trim>
      <trim prefix="`transaction_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.transactionType,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`original_filename` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.originalFilename,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`partner_credit_no` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.partnerCreditNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`credit_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.creditDate,jdbcType=DATE}
        </foreach>
      </trim>
      <trim prefix="`credit_amount` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.creditAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="`credit_status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.creditStatus,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`extended_fields_json` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.extendedFieldsJson,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`load_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when `id` = #{item.id,jdbcType=BIGINT} then #{item.loadTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
    </trim>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update `dz_raw_partner_credit_data`
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
    where `id` in
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`sync_batch_id` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.syncBatchId != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.syncBatchId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`channel_code` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.channelCode != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.channelCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`processing_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.processingDate != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.processingDate,jdbcType=DATE}
          </if>
        </foreach>
      </trim>
      <trim prefix="`transaction_type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.transactionType != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.transactionType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`original_filename` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.originalFilename != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.originalFilename,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`partner_credit_no` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.partnerCreditNo != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.partnerCreditNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`credit_date` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creditDate != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.creditDate,jdbcType=DATE}
          </if>
        </foreach>
      </trim>
      <trim prefix="`credit_amount` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creditAmount != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.creditAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="`credit_status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creditStatus != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.creditStatus,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`extended_fields_json` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.extendedFieldsJson != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.extendedFieldsJson,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`load_time` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.loadTime != null">
            when `id` = #{item.id,jdbcType=BIGINT} then #{item.loadTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.rongchen.byh.webadmin.upms.model.DzRawPartnerCreditDataEntity">
    <!--@mbg.generated-->
    update `dz_raw_partner_credit_data`
    <set>
      <if test="syncBatchId != null">
        `sync_batch_id` = #{syncBatchId,jdbcType=VARCHAR},
      </if>
      <if test="channelCode != null">
        `channel_code` = #{channelCode,jdbcType=VARCHAR},
      </if>
      <if test="processingDate != null">
        `processing_date` = #{processingDate,jdbcType=DATE},
      </if>
      <if test="transactionType != null">
        `transaction_type` = #{transactionType,jdbcType=VARCHAR},
      </if>
      <if test="originalFilename != null">
        `original_filename` = #{originalFilename,jdbcType=VARCHAR},
      </if>
      <if test="partnerCreditNo != null">
        `partner_credit_no` = #{partnerCreditNo,jdbcType=VARCHAR},
      </if>
      <if test="creditDate != null">
        `credit_date` = #{creditDate,jdbcType=DATE},
      </if>
      <if test="creditAmount != null">
        `credit_amount` = #{creditAmount,jdbcType=DECIMAL},
      </if>
      <if test="creditStatus != null">
        `credit_status` = #{creditStatus,jdbcType=VARCHAR},
      </if>
      <if test="extendedFieldsJson != null">
        `extended_fields_json` = #{extendedFieldsJson,jdbcType=VARCHAR},
      </if>
      <if test="loadTime != null">
        `load_time` = #{loadTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.rongchen.byh.webadmin.upms.model.DzRawPartnerCreditDataEntity">
    <!--@mbg.generated-->
    update `dz_raw_partner_credit_data`
    set `sync_batch_id` = #{syncBatchId,jdbcType=VARCHAR},
      `channel_code` = #{channelCode,jdbcType=VARCHAR},
      `processing_date` = #{processingDate,jdbcType=DATE},
      `transaction_type` = #{transactionType,jdbcType=VARCHAR},
      `original_filename` = #{originalFilename,jdbcType=VARCHAR},
      `partner_credit_no` = #{partnerCreditNo,jdbcType=VARCHAR},
      `credit_date` = #{creditDate,jdbcType=DATE},
      `credit_amount` = #{creditAmount,jdbcType=DECIMAL},
      `credit_status` = #{creditStatus,jdbcType=VARCHAR},
      `extended_fields_json` = #{extendedFieldsJson,jdbcType=VARCHAR},
      `load_time` = #{loadTime,jdbcType=TIMESTAMP}
    where `id` = #{id,jdbcType=BIGINT}
  </update>
</mapper>