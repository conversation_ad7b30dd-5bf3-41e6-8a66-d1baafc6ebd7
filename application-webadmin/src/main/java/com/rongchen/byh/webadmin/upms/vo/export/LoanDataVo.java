package com.rongchen.byh.webadmin.upms.vo.export;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class LoanDataVo {

    /**
     * 年月信息
     */
    @Schema(description = "年月信息，格式通常为 yyyy-MM")
    private String yearMonth;

    /**
     * 渠道名称
     */
    @Schema(description = "渠道名称")
    private String channelName;


    /**
     * 放款成功总人数
     */
    @Schema(description = "放款成功总人数")
    private Integer userCount;
    /**
     * 放款成功金额
     */
    @Schema(description = "放款成功金额")
    private BigDecimal creditAmount;
}
