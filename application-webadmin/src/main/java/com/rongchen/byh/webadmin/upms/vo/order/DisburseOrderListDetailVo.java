package com.rongchen.byh.webadmin.upms.vo.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @ClassName DisburseOrderListDetailVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/1/23 15:40
 * @Version 1.0
 **/
@Data
public class DisburseOrderListDetailVo {

    @Schema(description = "客户id")
    private String userId;

    @Schema(description = "客户名称")
    private String userName;

    @Schema(description = "客户身份证")
    private String idNumber;

    @Schema(description = "注册手机号")
    private String mobile;

    @Schema(description = "客户户籍地址")
    private String userAddress;

    @Schema(description = "积分余额")
    private String pointsBalance;

    @Schema(description = "职业")
    private String job;

    @Schema(description = "公司名称")
    private String firmName;

    @Schema(description = "公司电话")
    private String firmMobile;

    @Schema(description = "公司地址")
    private String firmAddress;

    @Schema(description = "家庭联系人")
    private String familyContact;

    @Schema(description = "家庭联系人号码")
    private String familyMobile;

    @Schema(description = "其他联系人")
    private String otherContact;

    @Schema(description = "其他联系人号码")
    private String otherMobile;

    @Schema(description = "会员")
    private String isVip;

    @Schema(description = "注册时间")
    private String userCreateTime;

    @Schema(description = "授信时间")
    private String creditCreateTime;

    @Schema(description = "渠道来源")
    private String channelName;

    @Schema(description = "借款单号")
    private String loanNo;

    @Schema(description = "授信流水号")
    private String creditNo;

    @Schema(description = "订单号")
    private Long disburseId ;

    @Schema(description = "产品类型")
    private String productTypeName;

    @Schema(description = "订单状态")
    private String disburseStatusName;

    @Schema(description = "消费时间")
    private String disburseCreateTime;

    @Schema(description = "商品数量")
    private Integer commodityNum;

    @Schema(description = "商品信息")
    private String commodityMessage;

    @Schema(description = "商品价格")
    private String commodityPrice ;

    @Schema(description = "收货人姓名")
    private String consigneeName;

    @Schema(description = "收货人电话")
    private String consigneeMobile;

    @Schema(description = "是否本人收货")
    private String isConsignee;

    @Schema(description = "分期金额")
    private String installmentAmount;

    @Schema(description = "年利率")
    private String yearRate;

    @Schema(description = "绑定银行")
    private String backName;

    @Schema(description = "银行卡号")
    private String backAccount;

    @Schema(description = "注册手机号与绑卡手机号是否一致")
    private String isIdentical;

    @Schema(description = "其他费用")
    private String otherAmount;

    @Schema(description = "其他费用利率")
    private String amountRate;

    @Schema(description = "其他费用类型")
    private String otherAmountType;

    @Schema(description = "平台授信记录")
    private List<DisbursePlatformCreditRecordVo> platformCreditRecordList;

    @Schema(description = "身份证正面")
    private String idNumberCorrectUrl;

    @Schema(description = "身份证反面")
    private String idNumberAntiUrl;

    @Schema(description = "人脸图像")
    private String facesUrl;

    @Schema(description = "通信录信息")
    private List<ContactsVo> contactsList;

    @Schema(description = "历史还款")
    private List<HistoricalRepaymentVo> historicalRepaymentList;

    @Schema(description = "探针征信源")
    private Object probe;

    @Schema(description = "雷达征信源")
    private Object radar;

    @Schema(description = "风控信息")
    private Object riskControlInformation;

    @Schema(description = "历史工单")
    private Object historicalWorkOrder;

    @Schema(description = "放款资金方")
    private String  fundCode;

    @Schema(description = "来源模式")
    private String  sourceMode;
}
