package com.rongchen.byh.webadmin.upms.vo.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @ClassName DisburseOrderListVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/1/23 11:49
 * @Version 1.0
 **/
@Data
public class DisburseOrderResultListVo {

    @Schema(description = "订单号")
    private Long disburseId;

    @Schema(description = "客户名称")
    private String userName;

    @Schema(description = "客户手机号")
    private String mobile;

    @Schema(description = "客户身份证")
    private String IdNumber;

    @Schema(description = "客户风险等级")
    private String riskLevelName;

    @Schema(description = "审核标识")
    private String auditStatusName;

    @Schema(description = "供应商")
    private String userSource;

    @Schema(description = "产品类型")
    private String productTypeName;

    @Schema(description = "支用时间")
    private String consumptionTime;

    @Schema(description = "申请金额")
    private String applyAmount;

    @Schema(description = "首付金额")
    private String downPayment;

    @Schema(description = "期数")
    private Integer repayTerm;

    @Schema(description = "月供")
    private String monthlyPayment;

    @Schema(description = "其他费用")
    private String otherAmount;

    @Schema(description = "渠道名称")
    private String channeName;

    @Schema(description = "门店名称")
    private String  storeName;

    @Schema(description = "销售员名称")
    private String  saleUserName;

    @Schema(description = "小组名称")
    private String  groupName;

    @Schema(description = "来源模式")
    private String  sourceMode;

    @Schema(description = "放款成功时间")
    private String loanTime;

    @Schema(description = "注册时间")
    private String registerTime;

}
