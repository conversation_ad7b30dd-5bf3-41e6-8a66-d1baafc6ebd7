package com.rongchen.byh.webadmin.reconciliation.job;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.rongchen.byh.webadmin.WebAdminBaseTest;
import com.rongchen.byh.webadmin.reconciliation.service.SftpDataSyncService;
import java.time.LocalDate;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@DisplayName("SFTP数据同步Job测试")
@Slf4j
public class SftpDataSyncJobTest extends WebAdminBaseTest {

    @Autowired
    private SftpDataSyncJob sftpDataSyncJob;

    @Autowired
    private SftpDataSyncService sftpDataSyncService;

    @Test
    @DisplayName("测试XXL-Job调度同步任务")
    void testSftpDataSyncJobHandler() throws Exception {

        sftpDataSyncJob.syncSftpData();

    }

    @Test
    @DisplayName("测试Service按指定日期同步")
    void testSyncDataForSpecificDate() {

        LocalDate testDate = LocalDate.of(2025, 5, 16);
        SftpDataSyncService.SyncResult result = sftpDataSyncService.syncDataForDate(testDate);
        log.info("result: {}", result);

    }

    @Test
    @DisplayName("测试Service按渠道和交易类型过滤同步")
    void testSyncDataForDateAndChannel() {

        LocalDate testDate = LocalDate.of(2025, 6, 2);
        String channelCode = "XHY";
        //LOAN, REPAYMENT, CREDIT
        String transactionType = "REPAYMENT";

        SftpDataSyncService.SyncResult result = sftpDataSyncService.syncDataForDateAndChannel(testDate, channelCode,
            transactionType);
        log.info("测试Service按渠道和交易类型过滤同步， result: {}", result);

    }

    @Test
    @DisplayName("测试Service重新同步功能")
    void testResyncDataForDate() {
        // Arrange
        LocalDate testDate = LocalDate.of(2024, 1, 15);
        boolean forceResync = true;
        SftpDataSyncService.SyncResult mockResult = new SftpDataSyncService.SyncResult(testDate);
        when(sftpDataSyncService.resyncDataForDate(testDate, forceResync)).thenReturn(mockResult);

        // Act
        SftpDataSyncService.SyncResult result = sftpDataSyncService.resyncDataForDate(testDate, forceResync);

        // Assert
        assertNotNull(result);
        assertEquals(testDate, result.getProcessingDate());
        verify(sftpDataSyncService).resyncDataForDate(testDate, forceResync);
    }

    @Test
    @DisplayName("测试同步结果统计功能")
    void testSyncResultStatistics() {
        // Arrange
        LocalDate testDate = LocalDate.of(2024, 1, 15);
        SftpDataSyncService.SyncResult result = new SftpDataSyncService.SyncResult(testDate);

        // Act & Assert
        assertEquals(0, result.getSuccessCount());
        assertEquals(0, result.getFailureCount());
        assertEquals(0, result.getSkippedCount());
        assertFalse(result.hasFailures());
        assertTrue(result.getFailedTasks().isEmpty());
        assertTrue(result.getTaskResults().isEmpty());
    }

    @Test
    @DisplayName("测试Job异常处理")
    void testJobExceptionHandling() {
        // Arrange
        when(sftpDataSyncService.syncDataForDate(any(LocalDate.class)))
            .thenThrow(new RuntimeException("测试异常"));

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            sftpDataSyncJob.syncSftpData();
        });

        verify(sftpDataSyncService).syncDataForDate(LocalDate.now().minusDays(1));
    }
}