package com.rongchen.byh.webadmin.reconciliation.job;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.webadmin.WebAdminBaseTest;
import com.rongchen.byh.webadmin.reconciliation.context.ReconciliationContext;
import com.rongchen.byh.webadmin.reconciliation.dataProvider.DataProvider;
import com.rongchen.byh.webadmin.reconciliation.dataProvider.DataProviderFactory;
import com.rongchen.byh.webadmin.reconciliation.mapper.EntityToNormalizedModelConverter;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedCreditRecord;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedLoanRecord;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedRepaymentRecord;
import com.rongchen.byh.webadmin.reconciliation.model.NormalizedTransaction;
import com.rongchen.byh.webadmin.upms.model.DzRawPartnerCreditDataEntity;
import com.rongchen.byh.webadmin.upms.model.DzRawPartnerLoanDataEntity;
import com.rongchen.byh.webadmin.upms.model.DzRawPartnerRepaymentDataEntity;
import com.rongchen.byh.webadmin.upms.service.DzRawPartnerCreditDataService;
import com.rongchen.byh.webadmin.upms.service.DzRawPartnerLoanDataService;
import com.rongchen.byh.webadmin.upms.service.DzRawPartnerRepaymentDataService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 数据加载测试类
 * <p>
 * 专门用于测试对账系统中的数据加载功能，包括我方数据和资方数据的加载。
 * 支持按交易类型（借款、还款、授信）分别测试数据加载是否正常。
 */
@Slf4j
@DisplayName("数据加载功能测试")
public class DataLoadingTest extends WebAdminBaseTest {

    @Autowired
    private DataProviderFactory dataProviderFactory;

    @Autowired
    private EntityToNormalizedModelConverter entityConverter;

    @Autowired
    private DzRawPartnerLoanDataService dzRawPartnerLoanDataService;

    @Autowired
    private DzRawPartnerRepaymentDataService dzRawPartnerRepaymentDataService;

    @Autowired
    private DzRawPartnerCreditDataService dzRawPartnerCreditDataService;

    // 测试配置
    private static final LocalDate TEST_PROCESSING_DATE = LocalDate.of(2025, 5, 16);
    private static final String TEST_CHANNEL_CODE = "XHY";

    /**
     * 测试我方借款数据加载
     */
    @Test
    @DisplayName("测试我方借款数据加载")
    public void testLoadOurSystemLoanData() {
        log.info("=== 开始测试我方借款数据加载 ===");
        
        try {
            // 获取我方借款数据提供者
            DataProvider ourDataProvider = dataProviderFactory.getDataProvider("MYSYSTEM", "LOAN", "OUR_SIDE");
            
            // 构建上下文
            ReconciliationContext context = buildContextForDataLoad(TEST_PROCESSING_DATE, "MYSYSTEM", "LOAN", 
                "TEST_OUR_LOAN_LOAD");
            
            // 加载数据
            List<? extends NormalizedTransaction> ourNormalizedRecords = ourDataProvider.loadNormalizedData(context);
            
            // 验证和统计
            validateAndLogDataResults("我方借款数据", ourNormalizedRecords, NormalizedLoanRecord.class);
            
        } catch (Exception e) {
            log.error("我方借款数据加载测试失败", e);
            throw new RuntimeException("我方借款数据加载测试失败", e);
        }
        
        log.info("=== 我方借款数据加载测试完成 ===");
    }

    /**
     * 测试我方还款数据加载
     */
    @Test
    @DisplayName("测试我方还款数据加载")
    public void testLoadOurSystemRepaymentData() {
        log.info("=== 开始测试我方还款数据加载 ===");
        
        try {
            // 获取我方还款数据提供者
            DataProvider ourDataProvider = dataProviderFactory.getDataProvider("MYSYSTEM", "REPAYMENT", "OUR_SIDE");
            
            // 构建上下文
            ReconciliationContext context = buildContextForDataLoad(TEST_PROCESSING_DATE, "MYSYSTEM", "REPAYMENT", 
                "TEST_OUR_REPAYMENT_LOAD");
            
            // 加载数据
            List<? extends NormalizedTransaction> ourNormalizedRecords = ourDataProvider.loadNormalizedData(context);
            
            // 验证和统计
            validateAndLogDataResults("我方还款数据", ourNormalizedRecords, NormalizedRepaymentRecord.class);
            
        } catch (Exception e) {
            log.error("我方还款数据加载测试失败", e);
            throw new RuntimeException("我方还款数据加载测试失败", e);
        }
        
        log.info("=== 我方还款数据加载测试完成 ===");
    }

    /**
     * 测试我方授信数据加载
     */
    @Test
    @DisplayName("测试我方授信数据加载")
    public void testLoadOurSystemCreditData() {
        log.info("=== 开始测试我方授信数据加载 ===");
        
        try {
            // 获取我方授信数据提供者
            DataProvider ourDataProvider = dataProviderFactory.getDataProvider("MYSYSTEM", "CREDIT", "OUR_SIDE");
            
            // 构建上下文
            ReconciliationContext context = buildContextForDataLoad(TEST_PROCESSING_DATE, "MYSYSTEM", "CREDIT", 
                "TEST_OUR_CREDIT_LOAD");
            
            // 加载数据
            List<? extends NormalizedTransaction> ourNormalizedRecords = ourDataProvider.loadNormalizedData(context);
            
            // 验证和统计
            validateAndLogDataResults("我方授信数据", ourNormalizedRecords, NormalizedCreditRecord.class);
            
        } catch (Exception e) {
            log.error("我方授信数据加载测试失败", e);
            throw new RuntimeException("我方授信数据加载测试失败", e);
        }
        
        log.info("=== 我方授信数据加载测试完成 ===");
    }

    /**
     * 构建数据加载上下文
     */
    private ReconciliationContext buildContextForDataLoad(LocalDate processingDate, String channelCode,
                                                         String transactionType, String batchId) {
        return ReconciliationContext.builder()
            .processingDate(processingDate)
            .channelCode(channelCode)
            .transactionType(transactionType)
            .batchId(batchId)
            .build();
    }

    /**
     * 计算总金额
     */
    private BigDecimal calculateTotalAmount(List<? extends NormalizedTransaction> records) {
        if (CollectionUtils.isEmpty(records)) {
            return BigDecimal.ZERO;
        }
        BigDecimal total = BigDecimal.ZERO;
        for (NormalizedTransaction record : records) {
            if (record != null && record.getReconciliationAmount() != null) {
                total = total.add(record.getReconciliationAmount());
            }
        }
        return total;
    }

    /**
     * 验证并记录数据结果
     */
    private void validateAndLogDataResults(String dataType, List<? extends NormalizedTransaction> records, 
                                         Class<?> expectedRecordType) {
        int count = records != null ? records.size() : 0;
        BigDecimal totalAmount = calculateTotalAmount(records);
        
        log.info("数据类型: {}", dataType);
        log.info("  记录数量: {} 条", count);
        log.info("  总金额: {}", totalAmount);
        log.info("  期望记录类型: {}", expectedRecordType.getSimpleName());
        
        if (count > 0) {
            NormalizedTransaction firstRecord = records.get(0);
            log.info("  实际记录类型: {}", firstRecord.getClass().getSimpleName());
            log.info("  源渠道: {}", firstRecord.getSourceChannel());
            log.info("  首条记录ID: {}", firstRecord.getOriginalRecordId());
            
            // 验证记录类型
            if (!expectedRecordType.isInstance(firstRecord)) {
                log.warn("  警告: 记录类型不匹配! 期望: {}, 实际: {}", 
                    expectedRecordType.getSimpleName(), firstRecord.getClass().getSimpleName());
            }
            
            // 显示前几条记录的详细信息
            int displayCount = Math.min(3, count);
            log.info("  前{}条记录详情:", displayCount);
            for (int i = 0; i < displayCount; i++) {
                NormalizedTransaction record = records.get(i);
                log.info("    [{}] ID: {}, 金额: {}, 渠道: {}", 
                    i + 1, record.getOriginalRecordId(), record.getReconciliationAmount(), record.getSourceChannel());
            }
        } else {
            log.warn("  警告: 未找到任何数据记录!");
        }
        
        log.info("  数据验证完成");
    }
}
