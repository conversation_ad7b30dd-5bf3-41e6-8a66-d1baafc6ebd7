package com.rongchen.byh.sale.constants;

import java.util.HashMap;
import java.util.Map;

public class AuditsStatusConstant {

    private AuditsStatusConstant(){}

    // 审核状态 0 待审核 1 审核通过 2 审核不通过  4 转人工
    public static final int ZERO = 0;
    public static final int ONE = 1;
    public static final int TWO = 2;
    public static final int FOUR = 4;

    private static final Map<Object, String> DICT_MAP = new HashMap<>(5);
    static {
        DICT_MAP.put(ZERO, "待审核");
        DICT_MAP.put(ONE, "审核通过");
        DICT_MAP.put(TWO, "审核不通过");
        DICT_MAP.put(FOUR, "转人工");
    }

    public static String getAuditsStatusName(Integer auditsStatus){
        return DICT_MAP.get(auditsStatus);
    }

}
