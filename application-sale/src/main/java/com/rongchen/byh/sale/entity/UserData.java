package com.rongchen.byh.sale.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 用户基础信息表
 * @TableName user_data
 */
@TableName(value ="user_data")
@Data
public class UserData implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 用户状态 1 正常  0 不正常
     */
    private Integer statusFlag;

    /**
     * 审核状态 0 未通过  1 通过
     */
    private Integer auditFlag;

    /**
     * 用户审核状态
     * 1-待提交初审（默认）:没有提交h5风控
     * 5-初审拒绝：h5风控拒绝
     * 10-待复审：h5风控通过，待员工审核
     * 15-复审拒绝：员工审核拒绝
     * 20-待App授信：待进入我方app授信
     * 25-已授信：我方app授信完成
     * 30-授信拒绝：我方app授信拒绝
     */
    private Integer auditStatus;

    /**
     * 手机号md
     */
    private String mobileMd;

    /**
     * 平台id
     */
    private Long channelId;

    /**
     * 注册ip
     */
    private String ip;

    /**
     * 注册时间
     */
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}