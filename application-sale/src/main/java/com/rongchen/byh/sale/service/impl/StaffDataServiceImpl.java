package com.rongchen.byh.sale.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.rongchen.byh.sale.entity.StaffData;
import com.rongchen.byh.sale.service.StaffDataService;
import com.rongchen.byh.sale.dao.StaffDataMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【staff_data(销售员工表)】的数据库操作Service实现
* @createDate 2025-02-07 10:48:19
*/
@Service
public class StaffDataServiceImpl extends ServiceImpl<StaffDataMapper, StaffData>
    implements StaffDataService{

    @Override
    public StaffData getByMobile(String mobile) {
        return this.baseMapper.selectByMobile(mobile);
    }
}




