package com.rongchen.byh.sale.utils;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.sale.dto.CreateTokenDto;

public class UserTokenUtil {

    private static final String TOKEN_USER_KEY = "tokenData";

    /**
     * 创建用户token
     * @param createTokenDto
     * @return
     */
    public static void createToken(CreateTokenDto createTokenDto) {
        Long loginId = StpUtil.getLoginId(createTokenDto.getUserId());
        StpUtil.login(loginId, createTokenDto.getDeviceType());
        String tokenValue = StpUtil.getTokenValue();
        createTokenDto.setToken(tokenValue);
        SaSession tokenSession = StpUtil.getTokenSession();
        tokenSession.set(TOKEN_USER_KEY, createTokenDto);
    }

    public static Long getUserId() {
        SaSession tokenSession = StpUtil.getTokenSession();
        CreateTokenDto createTokenDto = JSON.toJavaObject((JSONObject) tokenSession.get(TOKEN_USER_KEY),CreateTokenDto.class);
        return createTokenDto.getUserId();
    }

}
