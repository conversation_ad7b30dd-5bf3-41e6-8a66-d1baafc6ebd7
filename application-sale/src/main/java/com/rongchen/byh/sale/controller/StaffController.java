package com.rongchen.byh.sale.controller;


import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.lang.Validator;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.rongchen.byh.common.api.sms.service.SmsService;
import com.rongchen.byh.common.api.sms.vo.SendSmsVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.ContextUtil;
import com.rongchen.byh.common.core.util.IpUtil;
import com.rongchen.byh.sale.dto.CreateTokenDto;
import com.rongchen.byh.sale.dto.StaffLoginDto;
import com.rongchen.byh.sale.entity.StaffData;
import com.rongchen.byh.sale.service.StaffDataService;
import com.rongchen.byh.sale.utils.UserTokenUtil;
import com.rongchen.byh.sale.vo.LoginVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

@Tag(name = "员工相关接口")
@ApiSupport(order = 2)
@RestController
@RequestMapping("/staff")
public class StaffController {

    @Resource
    SmsService smsService;
    @Resource
    RedissonClient redissonClient;
    @Resource
    StaffDataService staffDataService;

    private static final String MOBILE_PREFIX = "smb:";
    private static final String IP_PREFIX = "sip:";
    private static final String YZM_PREFIX = "syzm:";

    @PostMapping("/sendCode")
    @Operation(summary = "发送验证码")
    @SaIgnore
    public ResponseResult<Void> sendCode(@RequestBody StaffLoginDto staffLoginDto) {
        String mobile = staffLoginDto.getMobile();
        if (!Validator.isMobile(mobile)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"手机号格式错误");
        }
        HttpServletRequest httpRequest = ContextUtil.getHttpRequest();
        String ipAddress = IpUtil.getRemoteIpAddress(httpRequest);
        // 一个手机号2分钟3条
        if (!checkMobile(mobile)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"短信发送太频繁，请稍后再试");
        }
        // 一个IP一天20条
        if (!checkIp(ipAddress)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"短信发送太频繁，请稍后再试");
        }

        ResponseResult<StaffData> checkResult = this.checkStaff(staffLoginDto.getMobile());
        if (!checkResult.isSuccess()) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,checkResult.getErrorMessage());
        }
        StaffData staffData = checkResult.getData();
        if (staffData.getStaffStatus().equals(1)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"账号禁用");
        }

        // 发送短信
        SendSmsVo vo = smsService.sendSms(mobile);
        if (!vo.getSuccessFlag()) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"短信发送失败，请稍后再试");
        }
        String msgId = vo.getMsgId();
        // 缓存验证码
        RBucket<String> bucket = redissonClient.getBucket(YZM_PREFIX + mobile);
        bucket.set(msgId+"", 2, TimeUnit.MINUTES);

        return ResponseResult.success();
    }

    private boolean checkIp(String ipAddress) {
        RBucket<Integer> bucket = redissonClient.getBucket(IP_PREFIX + ipAddress);
        Integer count = bucket.get();
        if (count == null) {
            bucket.set(1);
            bucket.expire(1, TimeUnit.DAYS);
            return true;
        }
        if (count >= 300) {
            return false;
        }
        bucket.set(count + 1,bucket.remainTimeToLive()/1000, TimeUnit.SECONDS);
        return true;
    }

    private boolean checkMobile(String mobile) {
        RBucket<Integer> bucket = redissonClient.getBucket(MOBILE_PREFIX + mobile);
        Integer count = bucket.get();
        if (count == null) {
            bucket.set(1);
            bucket.expire(60 * 2, TimeUnit.SECONDS);
            return true;
        }
        if (count >= 3) {
            return false;
        }
        bucket.set(count + 1,bucket.remainTimeToLive()/1000, TimeUnit.SECONDS);
        return true;
    }

    private ResponseResult<StaffData> checkStaff(String mobile) {
        StaffData staffData = staffDataService.getByMobile(mobile);
        if (staffData == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"手机号未注册");
        }
        return ResponseResult.success(staffData);
    }

    @PostMapping("/login")
    @Operation(summary = "用户登录")
    @SaIgnore
    public ResponseResult<LoginVo> login(@RequestBody StaffLoginDto staffLoginDto) {
        if(!Validator.isMobile(staffLoginDto.getMobile())) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"手机号格式错误");
        }
        ResponseResult<StaffData> checkResult = this.checkStaff(staffLoginDto.getMobile());
        if (!checkResult.isSuccess()) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,checkResult.getErrorMessage());
        }
        StaffData staffData = checkResult.getData();
        if (staffData.getStaffStatus().equals(1)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"账号禁用");
        }
        RBucket<String> bucket = redissonClient.getBucket(YZM_PREFIX + staffLoginDto.getMobile());
        String msgId = bucket.get();
        if (msgId == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"验证码已过期，请重新获取");
        }
        SendSmsVo vo = smsService.verifyCode(msgId, staffLoginDto.getCode());
        if (!vo.getSuccessFlag()) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"验证码错误");
        }

        Long userId = staffData.getId();

        CreateTokenDto createTokenDto = new CreateTokenDto();
        createTokenDto.setUserId(userId);
        createTokenDto.setAccount(staffData.getMobile());

        UserTokenUtil.createToken(createTokenDto);

        LoginVo loginVo = new LoginVo();
        loginVo.setUserId(userId);
        loginVo.setUserName(staffData.getUserName());
        loginVo.setMobile(staffData.getMobile());
        loginVo.setToken(createTokenDto.getToken());

        return ResponseResult.success(loginVo);
    }

}
