package com.rongchen.byh.sale.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.rongchen.byh.sale.constants.AuditStatusConstant;
import com.rongchen.byh.sale.constants.AuditsStatusConstant;
import com.rongchen.byh.sale.constants.ConsultFeeConstant;
import com.rongchen.byh.sale.constants.LoanStatusConstant;
import com.rongchen.byh.sale.dao.CreditManageMapper;
import com.rongchen.byh.sale.dto.filter.CreditOrderFilter;
import com.rongchen.byh.sale.dto.filter.LoanOrderFilter;
import com.rongchen.byh.sale.service.CreditManageService;
import com.rongchen.byh.sale.vo.CreditOrderVo;
import com.rongchen.byh.sale.vo.LoanOrderVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;


@Service
public class CreditManageServiceImpl implements CreditManageService {

    @Resource
    CreditManageMapper creditManageMapper;


    @Override
    public List<CreditOrderVo> creditOrderList(CreditOrderFilter filter) {
        String orderBy = "user_data.create_time desc";
        List<CreditOrderVo> list = creditManageMapper.creditOrderList(filter, orderBy);
        if (CollectionUtil.isNotEmpty(list)) {
            for (CreditOrderVo orderVo : list) {
                orderVo.setAuditsStatusName(AuditsStatusConstant.getAuditsStatusName(orderVo.getAuditsStatus()));
                orderVo.setAuditStatusName(AuditStatusConstant.getAuditStatusName(orderVo.getAuditStatus()));
                if (orderVo.getOnlineType() != null) {
                    orderVo.setModelName(orderVo.getOnlineType() == 1 ? "友帮贷" : "闪易贷");
                }
            }
        }
        return list;
    }

    @Override
    public List<LoanOrderVo> loanOrderList(LoanOrderFilter filter) {
        String orderBy = "disburse_data.credit_status asc,disburse_data.create_time desc";
        List<LoanOrderVo> list = creditManageMapper.loanOrderList(filter, orderBy);
        if (CollectionUtil.isNotEmpty(list)) {
            for (LoanOrderVo orderVo : list) {
                orderVo.setConsultFeeName(ConsultFeeConstant.getConsultFeeName(orderVo.getConsultFee()));
                orderVo.setCreditStatusName(LoanStatusConstant.getName(orderVo.getCreditStatus()));
            }
        }
        return list;
    }
}
