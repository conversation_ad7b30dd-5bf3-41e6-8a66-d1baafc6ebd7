<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.sale.dao.UserDetailMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.sale.entity.UserDetail">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="webName" column="web_name" jdbcType="VARCHAR"/>
            <result property="webIdCard" column="web_id_card" jdbcType="VARCHAR"/>
            <result property="webTwoElements" column="web_two_elements" jdbcType="INTEGER"/>
            <result property="riskLevel" column="risk_level" jdbcType="VARCHAR"/>
            <result property="headUrl" column="head_url" jdbcType="VARCHAR"/>
            <result property="appName" column="app_name" jdbcType="VARCHAR"/>
            <result property="sex" column="sex" jdbcType="VARCHAR"/>
            <result property="nation" column="nation" jdbcType="VARCHAR"/>
            <result property="birth" column="birth" jdbcType="VARCHAR"/>
            <result property="address" column="address" jdbcType="VARCHAR"/>
            <result property="idNumber" column="id_number" jdbcType="VARCHAR"/>
            <result property="authority" column="authority" jdbcType="VARCHAR"/>
            <result property="validDate" column="valid_date" jdbcType="VARCHAR"/>
            <result property="idCardFrondUrl" column="id_card_frond_url" jdbcType="VARCHAR"/>
            <result property="idCardReverseUrl" column="id_card_reverse_url" jdbcType="VARCHAR"/>
            <result property="ocrResult" column="ocr_result" jdbcType="INTEGER"/>
            <result property="faceUrl" column="face_url" jdbcType="VARCHAR"/>
            <result property="faceScore" column="face_score" jdbcType="VARCHAR"/>
            <result property="faceConfidence" column="face_confidence" jdbcType="VARCHAR"/>
            <result property="faceSource" column="face_source" jdbcType="VARCHAR"/>
            <result property="faceTime" column="face_time" jdbcType="TIMESTAMP"/>
            <result property="faceResult" column="face_result" jdbcType="INTEGER"/>
            <result property="educationLevel" column="education_level" jdbcType="VARCHAR"/>
            <result property="maritalStatus" column="marital_status" jdbcType="VARCHAR"/>
            <result property="houseStatus" column="house_status" jdbcType="VARCHAR"/>
            <result property="custAddress" column="cust_address" jdbcType="VARCHAR"/>
            <result property="custAddressProvice" column="cust_address_provice" jdbcType="VARCHAR"/>
            <result property="custAddressCity" column="cust_address_city" jdbcType="VARCHAR"/>
            <result property="custAddressCounty" column="cust_address_county" jdbcType="VARCHAR"/>
            <result property="incomeMonth" column="income_month" jdbcType="VARCHAR"/>
            <result property="relationshipOne" column="relationship_one" jdbcType="VARCHAR"/>
            <result property="emergencyNameOne" column="emergency_name_one" jdbcType="VARCHAR"/>
            <result property="emergencyMobileOne" column="emergency_mobile_one" jdbcType="VARCHAR"/>
            <result property="relationshipTwo" column="relationship_two" jdbcType="VARCHAR"/>
            <result property="emergencyNameTwo" column="emergency_name_two" jdbcType="VARCHAR"/>
            <result property="emergencyMobileTwo" column="emergency_mobile_two" jdbcType="VARCHAR"/>
            <result property="formFlag" column="form_flag" jdbcType="INTEGER"/>
            <result property="formTime" column="form_time" jdbcType="TIMESTAMP"/>
            <result property="phoneNumber" column="phone_number" jdbcType="VARCHAR"/>
            <result property="mobileStartTime" column="mobile_start_time" jdbcType="TIMESTAMP"/>
            <result property="wifiSensitive" column="wifi_sensitive" jdbcType="VARCHAR"/>
            <result property="addressBookNum" column="address_book_num" jdbcType="INTEGER"/>
            <result property="addressBookMobileNum" column="address_book_mobile_num" jdbcType="INTEGER"/>
            <result property="addressBookSensitive" column="address_book_sensitive" jdbcType="VARCHAR"/>
            <result property="contactOperator" column="contact_operator" jdbcType="VARCHAR"/>
            <result property="overdueMessageNum" column="overdue_message_num" jdbcType="INTEGER"/>
            <result property="longitude" column="longitude" jdbcType="VARCHAR"/>
            <result property="latitude" column="latitude" jdbcType="VARCHAR"/>
            <result property="deviceBrand" column="device_brand" jdbcType="VARCHAR"/>
            <result property="networkType" column="network_type" jdbcType="VARCHAR"/>
            <result property="devAlias" column="dev_alias" jdbcType="VARCHAR"/>
            <result property="deviceId" column="device_id" jdbcType="VARCHAR"/>
            <result property="clientIp" column="client_ip" jdbcType="VARCHAR"/>
            <result property="coordinateType" column="coordinate_type" jdbcType="VARCHAR"/>
            <result property="gpsCity" column="gps_city" jdbcType="VARCHAR"/>
            <result property="lbsAddress" column="lbs_address" jdbcType="VARCHAR"/>
            <result property="gpsAddress" column="gps_address" jdbcType="VARCHAR"/>
            <result property="os" column="os" jdbcType="VARCHAR"/>
            <result property="osVersion" column="os_version" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,web_name,
        web_id_card,web_two_elements,risk_level,
        head_url,app_name,sex,
        nation,birth,address,
        id_number,authority,valid_date,
        id_card_frond_url,id_card_reverse_url,ocr_result,
        face_url,face_score,face_confidence,
        face_source,face_time,face_result,
        education_level,marital_status,house_status,
        cust_address,cust_address_provice,cust_address_city,
        cust_address_county,income_month,relationship_one,
        emergency_name_one,emergency_mobile_one,relationship_two,
        emergency_name_two,emergency_mobile_two,form_flag,
        form_time,phone_number,mobile_start_time,
        wifi_sensitive,address_book_num,address_book_mobile_num,
        address_book_sensitive,contact_operator,overdue_message_num,
        longitude,latitude,device_brand,
        network_type,dev_alias,device_id,
        client_ip,coordinate_type,gps_city,
        lbs_address,gps_address,os,
        os_version,create_time
    </sql>
    <select id="selectByUserId" resultType="com.rongchen.byh.sale.entity.UserDetail">
        select <include refid="Base_Column_List"/>
        from user_detail
        where user_id = #{userId}
    </select>
    <select id="selectInfoByUserId" resultType="com.rongchen.byh.sale.entity.UserInfo">
        select
            user_data.id,
            user_data.mobile,
            user_detail.web_name
        from user_data
        left join user_detail on user_data.id = user_detail.user_id
        where user_id = #{userId}
    </select>
</mapper>
