<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.sale.dao.StaffDataMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.sale.entity.StaffData">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userName" column="user_name" jdbcType="VARCHAR"/>
            <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
            <result property="inviteCode" column="invite_code" jdbcType="VARCHAR"/>
            <result property="inviteFlag" column="invite_flag" jdbcType="INTEGER"/>
            <result property="staffStatus" column="staff_status" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_name,mobile,
        invite_code,invite_flag,staff_status,
        create_time,update_time
    </sql>
    <select id="selectByMobile" resultType="com.rongchen.byh.sale.entity.StaffData">
        select <include refid="Base_Column_List" />
        from staff_data
        where mobile = #{mobile}
    </select>
</mapper>
