package com.rongchen.byh.common.rabbitmq.config;


import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RabbitConfig {


    @Bean(QueueConstant.LOAN_SUCCESS_QUEUE)
    public Queue loanSuccQueue() {
        return new Queue(QueueConstant.LOAN_SUCCESS_QUEUE);
    }

    /**
     * 放款成功api队列
     * @return
     */
    @Bean(QueueConstant.LOAN_SUCCESS_API_QUEUE)
    public Queue loanSuccApiQueue() {
        return new Queue(QueueConstant.LOAN_SUCCESS_API_QUEUE);
    }

    @Bean(QueueConstant.REPAY_QUEUE)
    public Queue repayQueue() {
        return new Queue(QueueConstant.REPAY_QUEUE);
    }


    @Bean(QueueConstant.SALE_QUEUE)
    public Queue sealQueue() {
        return new Queue(QueueConstant.SALE_QUEUE);
    }

    @Bean(QueueConstant.UPDATE_REPAY_OVERDUE_QUEUE)
    public Queue repayOverdueQueue() {
        return new Queue(QueueConstant.UPDATE_REPAY_OVERDUE_QUEUE);
    }


    @Bean(QueueConstant.REPAY_SMS_QUEUE)
    public Queue repaySmsQueue() {
        return new Queue(QueueConstant.REPAY_SMS_QUEUE);
    }

    @Bean(QueueConstant.TODAY_REPAY_SMS_QUEUE)
    public Queue todayRepaySmsQueue() {
        return new Queue(QueueConstant.TODAY_REPAY_SMS_QUEUE);
    }
    @Bean(QueueConstant.RISK_CONTROL_QUEUE)
    public Queue riskControl() {
        return new Queue(QueueConstant.RISK_CONTROL_QUEUE);
    }
    @Bean(QueueConstant.RISK_CONTROL_QUEUE_NEW)
    public Queue riskControlNew() {
        return new Queue(QueueConstant.RISK_CONTROL_QUEUE_NEW);
    }

    @Bean(QueueConstant.CREDIT_APPLY_QUEUE)
    public Queue creditApplyQueue() {
        return new Queue(QueueConstant.CREDIT_APPLY_QUEUE);
    }

    @Bean(QueueConstant.MAYI_API_CREDIT_QUEUE)
    public Queue mayiApiCreditQueue() {
        return new Queue(QueueConstant.MAYI_API_CREDIT_QUEUE);
    }


    @Bean(QueueConstant.USER_CAPITAL_CREDIT_APPLY_QUEUE)
    public Queue userCapitalCreditApplyQueue() {
        return new Queue(QueueConstant.USER_CAPITAL_CREDIT_APPLY_QUEUE);
    }

    @Bean(QueueConstant.JUYOU_API_CREDIT_QUEUE)
    public Queue juyouApiCreditQueue() {
        return new Queue(QueueConstant.JUYOU_API_CREDIT_QUEUE);
    }


}
