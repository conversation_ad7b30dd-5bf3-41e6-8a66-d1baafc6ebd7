package com.rongchen.byh.common.ext.constant;

/**
 * 业务组件数据源类型常量类。
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
public class BizWidgetDatasourceType {

    /**
     * 通用用户组件数据源类型。
     */
    public static final String UPMS_USER_TYPE = "upms_user";

    /**
     * 通用部门组件数据源类型。
     */
    public static final String UPMS_DEPT_TYPE = "upms_dept";

    /**
     * 通用角色组件数据源类型。
     */
    public static final String UPMS_ROLE_TYPE = "upms_role";

    /**
     * 通用岗位组件数据源类型。
     */
    public static final String UPMS_POST_TYPE = "upms_post";

    /**
     * 通用部门岗位组件数据源类型。
     */
    public static final String UPMS_DEPT_POST_TYPE = "upms_dept_post";

    /**
     * 私有构造函数，明确标识该常量类的作用。
     */
    private BizWidgetDatasourceType() {
    }
}
