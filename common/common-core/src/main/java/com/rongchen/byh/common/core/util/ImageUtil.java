package com.rongchen.byh.common.core.util;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.Transparency;
import java.awt.image.BufferedImage;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import javax.imageio.ImageIO;
import javax.imageio.ImageReader;
import javax.imageio.stream.ImageInputStream;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import net.coobird.thumbnailator.tasks.UnsupportedFormatException;


/**
 * <AUTHOR>
 * @version 2.0 优化版
 * @description 高性能图片工具类，用于压缩和合并图片。
 *              针对性能进行了优化，使用流式处理、二分查找压缩质量和 Graphics2D 合并。
 * @date 2025/2/22 14:39:33 (优化于 YYYY/MM/DD)
 */
@Slf4j
public class ImageUtil {

    private static final long DEFAULT_TARGET_SIZE_BYTES = 300 * 1024; // 默认目标大小：200KB
    private static final int COMPRESSION_ITERATION_LIMIT = 10; // 压缩质量二分查找的最大迭代次数
    private static final float QUALITY_PRECISION_THRESHOLD = 0.01f; // 质量调整的阈值
    private static final int IMAGE_WITH_REAR_MAX = 3;

    /**
     * 记录当前内存使用情况。
     * 
     * @param step 描述当前操作步骤的字符串。
     */
    private static void logMemoryUsage(String step) {
        Runtime runtime = Runtime.getRuntime();
        long maxMemory = runtime.maxMemory();
        long allocatedMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = allocatedMemory - freeMemory;
        log.info("[Memory Log - {}] Max: {} MB, Allocated: {} MB, Free: {} MB, Used: {} MB",
                step,
                maxMemory / (1024 * 1024),
                allocatedMemory / (1024 * 1024),
                freeMemory / (1024 * 1024),
                usedMemory / (1024 * 1024));
    }

    /**
     * @deprecated 主要用于测试的 main 方法，应移除或放在测试类中。
     */
    @Deprecated
    public static void main(String[] args) {

        // 测试新的 merge 方法 (带预调整大小)
        System.out.println("\\n--- 开始测试 merge (带预调整大小) ---");
        String imgLarge1 = "D:\\test\\id_up.jpeg"; // > 1080px wide, 替换为实际大图路径
        String imgLarge2 = "D:\\test\\id_down.jpeg"; // > 10000px high (不太可能), 替换为实际大图路径
        String mergedPreResizePath = "D:\\test\\merged_pre_resized.jpg";
        // 预期: imgLarge1 和 imgLarge2 会被缩小, imgSmall 不会 (如果未提供替换路径, 测试可能失败)
        long mergePreStart = System.nanoTime();
        boolean mergedPre = merge(new String[] { imgLarge1, imgLarge2 }, mergedPreResizePath, 1080, 1080);
        long mergePreEnd = System.nanoTime();
        System.out.println("带预调整大小的合并成功: " + mergedPre + ", 输出: " + mergedPreResizePath + ", 耗时: "
                + TimeUnit.NANOSECONDS.toMillis(mergePreEnd - mergePreStart) + " ms");
        System.out.println("-------------------------------------");

        /*System.out.println("--- 开始测试 compressAndResize：mergedPreResizePath ---");
        long compressStart = System.nanoTime();
        String compressedPath = compressAndResize(mergedPreResizePath, DEFAULT_TARGET_SIZE_BYTES, 1080, 1080);
        long compressEnd = System.nanoTime();
        System.out.println("compressAndResize 完成, 输出: " + compressedPath + ", 耗时: "
                + TimeUnit.NANOSECONDS.toMillis(compressEnd - compressStart) + " ms");
        System.out.println("----------------------------------");

        // 测试旧的 merge 方法调用 (应委托给新的，但不调整大小)
        System.out.println("\\n--- 开始测试 merge (不带预调整大小, 旧方法委托) ---");
        String mergedNoPreResizePath = "D:\\test\\merged_no_pre_resize.png";
        // 使用 imgLarge1 和 imgSmall 进行测试
        long mergeNoPreStart = System.nanoTime();
        boolean mergedNoPre = merge(new String[] { imgLarge1, imgLarge2 }, mergedNoPreResizePath); // 调用旧方法
        long mergeNoPreEnd = System.nanoTime();
        System.out.println("不带预调整大小的合并 (通过旧方法调用) 成功: " + mergedNoPre + ", 输出: " + mergedNoPreResizePath + ", 耗时: "
                + TimeUnit.NANOSECONDS.toMillis(mergeNoPreEnd - mergeNoPreStart) + " ms");
        System.out.println("---------------------------------------------------");

        // 测试新的内存优化方法 mergeResizeCompressToByteArray (并行版)
        System.out.println("\\n--- 开始测试 mergeResizeCompressToByteArray (内存优化+并行读取) ---");
        int memTargetMaxWidth = 1080;
        int memTargetMaxHeight = 1080;
        long memTargetSizeBytes = 300 * 1024;
        String memOutputFormat = "jpg";
        // 复用之前的 imgLarge1, imgLarge2
        ExecutorService testExecutor = Executors.newFixedThreadPool(2);
        try {

            long memMergeStart = System.nanoTime();
            byte[] resultBytes = mergeResizeCompressToByteArray(new String[] { imgLarge1, imgLarge2 },
                    memTargetMaxWidth, memTargetMaxHeight,
                    memTargetSizeBytes, memOutputFormat,
                    testExecutor); // 传递 Executor
            long memMergeEnd = System.nanoTime();
            long memDuration = TimeUnit.NANOSECONDS.toMillis(memMergeEnd - memMergeStart);

            if (resultBytes != null) {
                System.out.println("内存优化合并与压缩成功, 输出字节数组长度: " + resultBytes.length + ", 耗时: " + memDuration + " ms");
                // 可选：将字节数组写入文件以供验证

                try {
                    File memOutputFile = new File("D:\\test\\merged_memory_optimized.jpg");
                    try (FileOutputStream fos = new FileOutputStream(memOutputFile)) {
                        fos.write(resultBytes);
                    }
                    System.out.println("内存优化结果已写入文件: " + memOutputFile.getAbsolutePath());
                } catch (IOException e) {
                    System.err.println("写入内存优化结果文件时出错: " + e.getMessage());
                }

            } else {
                System.out.println("内存优化合并与压缩失败, 耗时: " + memDuration + " ms");
            }
            System.out.println("---------------------------------------------------------");
        } finally {
            testExecutor.shutdown();
        }*/
    }

    /**
     * 将图片文件压缩到指定的目标大小以下。
     *
     * @param imagePath       源图片文件的路径。
     * @param targetSizeBytes 目标文件大小（字节）。
     * @return 压缩后的图片文件路径、原始路径或 null（发生错误时）。
     */
    public static String compress(String imagePath, long targetSizeBytes) {
        File inputFile = new File(imagePath);
        // 检查输入文件是否存在、是否为文件、是否可读
        if (!inputFile.exists() || !inputFile.isFile() || !inputFile.canRead()) {
            log.error("压缩错误：输入文件未找到或不可读: {}", imagePath);
            return null;
        }

        long originalSize = inputFile.length();
        long startTime = System.nanoTime(); // 开始计时

        // 如果原始大小已小于等于目标大小，则无需压缩
        if (originalSize <= targetSizeBytes) {
            log.info("压缩跳过：图片大小 ({}) 已低于目标大小 ({}): {}",
                    originalSize, targetSizeBytes, imagePath);
            return imagePath; // 不需要压缩
        }

        // 获取原始文件扩展名
        String originalExtension = getFileExtension(imagePath);
        if (originalExtension == null) {
            log.error("压缩错误：无法确定文件扩展名: {}", imagePath);
            return null;
        }
        // 生成压缩后文件的路径 (例如：xxx_thumb.jpg)
        String newImagePath = imagePath.substring(0, imagePath.lastIndexOf(".")) + "_thumb." + originalExtension;

        log.info("开始压缩: 文件 {}, 原始大小: {} 字节, 目标大小: {} 字节", imagePath, originalSize, targetSizeBytes);
        // 使用 try-with-resources 确保流被关闭
        try (InputStream inputStream = new BufferedInputStream(Files.newInputStream(inputFile.toPath()));
                OutputStream outputStream = new BufferedOutputStream(Files.newOutputStream(Paths.get(newImagePath)))) {

            // 调用核心压缩逻辑
            boolean success = compressStreamToTargetSize(inputStream, outputStream, targetSizeBytes, imagePath);

            if (success) {
                long endTime = System.nanoTime(); // 结束计时
                long durationMillis = TimeUnit.NANOSECONDS.toMillis(endTime - startTime); // 计算耗时
                long finalSize = new File(newImagePath).length();
                log.info("压缩成功：原始大小: {} 字节, 最终大小: {} 字节。保存至: {}, 耗时: {} ms",
                        originalSize, finalSize, newImagePath, durationMillis);
                return newImagePath; // 返回压缩后的路径
            } else {
                long endTime = System.nanoTime(); // 结束计时 (失败情况)
                long durationMillis = TimeUnit.NANOSECONDS.toMillis(endTime - startTime); // 计算耗时
                log.warn("压缩失败或未达到目标大小: {}. 返回原始路径。耗时: {} ms", imagePath, durationMillis);
                // 可选：删除可能不完整或过大的压缩文件
                FileUtil.del(newImagePath);
                return imagePath; // 根据需求，失败时也可以返回 null
            }
        } catch (IOException e) {
            log.error("文件压缩 IO 错误: {}", imagePath, e);
            FileUtil.del(newImagePath); // 清理可能产生的局部文件
            return null; // 指示错误
        } catch (Exception e) { // 捕获更广泛的异常
            log.error("文件压缩时发生意外错误: {}", imagePath, e);
            // 在异常情况下也尝试记录时间，如果 startTime 已被初始化
            if (startTime > 0) {
                long endTime = System.nanoTime();
                long durationMillis = TimeUnit.NANOSECONDS.toMillis(endTime - startTime);
                log.error("压缩异常发生，耗时: {} ms", durationMillis);
            }
            FileUtil.del(newImagePath);
            return null;
        }
    }

    /**
     * 核心压缩逻辑：使用流和基于质量的二分查找。
     * 尝试将输入流中的图片压缩到 targetSizeBytes 以下，并将结果写入输出流。
     * 注意：此实现首先将整个 InputStream 读入一个字节数组，以便 Thumbnailator 可以进行迭代质量调整
     * （因为标准 InputStream 通常不可重置）。这假设源图片在压缩前的大小适合在可用内存中缓存。
     *
     * @param inputStream     源图片输入流。
     * @param outputStream    压缩后图片的输出流。
     * @param targetSizeBytes 目标大小（字节）。
     * @param imageId         用于日志记录的标识符（例如，原始路径）。
     * @return 如果压缩后的大小低于目标，则返回 true，否则返回 false。
     * @throws IOException 如果在读/写过程中发生 IO 错误。
     */
    private static boolean compressStreamToTargetSize(InputStream inputStream, OutputStream outputStream,
            long targetSizeBytes, String imageId) throws IOException {
        logMemoryUsage("compressStreamToTargetSize:Start");
        byte[] imageBytes;
        try {
            long readStartTime = System.nanoTime();
            logMemoryUsage("compressStreamToTargetSize:BeforeReadBytes");
            imageBytes = IoUtil.readBytes(inputStream, true);
            logMemoryUsage("compressStreamToTargetSize:AfterReadBytes");
            long readEndTime = System.nanoTime();
            log.debug("读取输入流 {} 字节耗时: {} ms", imageBytes.length,
                    TimeUnit.NANOSECONDS.toMillis(readEndTime - readStartTime));

            // 如果原始大小已满足要求，直接写入并返回
            if (imageBytes.length <= targetSizeBytes) {
                outputStream.write(imageBytes);
                log.info("图片 {} 大小 {} 已满足目标 {}，无需压缩。", imageId, imageBytes.length, targetSizeBytes);
                logMemoryUsage("compressStreamToTargetSize:SuccessReturnTrue");
                return true;
            }

            log.debug("开始对图片 {} (大小: {}) 进行基于质量的二分压缩，目标大小: {}", imageId, imageBytes.length,
                    targetSizeBytes);

            // 二分查找参数
            float minQuality = 0.0f;
            float maxQuality = 1.0f;
            float currentQuality = 0.75f; // 初始猜测值
            byte[] bestCompressedBytes = null; // 存储找到的最佳结果
            int iterations = 0;
            long searchStartTime = System.nanoTime();

            logMemoryUsage("compressStreamToTargetSize:BeforeLoop");
            while (minQuality <= maxQuality && iterations < COMPRESSION_ITERATION_LIMIT) {
                iterations++;
                ByteArrayOutputStream tempOutputStream = new ByteArrayOutputStream();
                try {
                    logMemoryUsage("compressStreamToTargetSize:LoopIteration" + iterations + "BeforeCompress");
                    Thumbnails.of(new ByteArrayInputStream(imageBytes)) // 从内存字节数组读取
                            .scale(1.0) // 保持原始尺寸，仅调整质量
                            .outputQuality(currentQuality) // 设置输出质量
                            .toOutputStream(tempOutputStream); // 输出到临时内存流
                    logMemoryUsage("compressStreamToTargetSize:LoopIteration" + iterations + "AfterCompress");

                    byte[] compressedBytes = tempOutputStream.toByteArray();
                    log.debug("迭代 {}: 质量={:.3f}, 大小={}", iterations, currentQuality, compressedBytes.length);

                    if (compressedBytes.length > 0 && compressedBytes.length <= targetSizeBytes) {
                        // 找到了满足条件的压缩结果，但尝试寻找更接近目标的更好质量
                        bestCompressedBytes = compressedBytes;
                        minQuality = currentQuality; // 提高下限，尝试更高质量
                    } else {
                        // 当前质量压缩后大小仍过大，或者压缩失败（大小为0）
                        maxQuality = currentQuality; // 降低上限，尝试更低质量
                    }

                    // 更新下一次迭代的质量 (二分法中点)
                    // 避免浮点数精度问题导致死循环
                    float nextQuality = (minQuality + maxQuality) / 2.0f;
                    if (Math.abs(nextQuality - currentQuality) < QUALITY_PRECISION_THRESHOLD) {
                        log.debug("质量调整过小 ({}), 停止迭代。", Math.abs(nextQuality - currentQuality));
                        break; // 质量变化足够小，停止搜索
                    }
                    currentQuality = nextQuality;

                } catch (UnsupportedFormatException e) {
                    log.error("压缩失败 {}: 不支持的图片格式。", imageId, e);
                    logMemoryUsage("compressStreamToTargetSize:ErrorReturnFalse");
                    return false; // 无法压缩此格式
                } catch (IOException e) {
                    log.warn("压缩迭代失败 imageId={}: quality={:.3f}", imageId, currentQuality, e);
                    logMemoryUsage("compressStreamToTargetSize:ErrorReturnFalse");
                    // 决定是停止还是继续？继续可能重复错误。选择停止。
                    return false;
                } finally {
                    IoUtil.close(tempOutputStream);
                }
            } // end while loop

            long searchEndTime = System.nanoTime();
            long searchDurationMillis = TimeUnit.NANOSECONDS.toMillis(searchEndTime - searchStartTime);

            // 循环结束后，检查是否找到了最佳结果
            if (bestCompressedBytes != null) {
                outputStream.write(bestCompressedBytes);
                log.info("核心压缩完成 {}: 找到质量 ≈{:.3f} ({} 次迭代), 最终大小: {} 字节, 查找耗时: {} ms",
                        imageId, minQuality, iterations, bestCompressedBytes.length, searchDurationMillis);
                logMemoryUsage("compressStreamToTargetSize:SuccessReturnTrue");
                return true; // 成功找到符合要求的质量设置
            } else {
                // 即使是最低质量的尝试也可能失败或超过大小
                log.warn("核心压缩失败 {}: 无法在 {} 次迭代内将大小降至 {} 字节以下。查找耗时: {} ms",
                        imageId, iterations, targetSizeBytes, searchDurationMillis);
                logMemoryUsage("compressStreamToTargetSize:FailReturnFalse");
                // 是否写入原始字节？或者什么都不写？不写似乎更安全。
                return false; // 指示未能达到目标
            }
        } catch (IOException e) { // Moved catch block outside the main logic try scope
            log.error("处理图片 {} 时发生外部IO错误: {}", imageId, e.getMessage(), e);
            logMemoryUsage("compressStreamToTargetSize:OuterCatch");
            throw e; // Re-throw exception after logging
        }
    }

    // --- 已废弃的方法 --- (保留供参考或移除)

    /**
     * @deprecated 已被使用流和二分查找的 compress 方法取代。
     */
    @Deprecated
    public static byte[] compressPicForScale(byte[] imageBytes, long desFileSize, String imageId) {
        log.warn("调用了已废弃的方法 compressPicForScale: {}", imageId);
        // 在过渡期间提供简单传递或基本实现
        if (imageBytes == null || imageBytes.length <= desFileSize * 1024) {
            return imageBytes;
        }
        // 可以回退到单次压缩尝试
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            // 调用新的核心逻辑进行一次尝试
            compressStreamToTargetSize(new ByteArrayInputStream(imageBytes), outputStream, desFileSize * 1024, imageId);
            return outputStream.toByteArray();
        } catch (IOException e) {
            log.error("在已废弃的 compressPicForScale 回退逻辑中发生错误: {}", imageId, e);
            return imageBytes; // 发生错误时返回原始字节
        }
    }

    // --- 图片合并逻辑 ---

    /**
     * 使用 Graphics2D 垂直合并两张图片，以获得更好的性能。
     * 新增：合并前可选择性地预先缩小源图片尺寸，以减少大图合并的 I/O 开销。
     * 结果图片的宽度将是（可能缩小的）源图片中的最大宽度。
     * 较窄的图片将水平居中。
     *
     * @param sourceImagePaths     包含两个源图片路径的数组。长度必须为 2。
     * @param savePath             合并后图片的保存路径。格式由扩展名决定。
     * @param maxWidthBeforeMerge  合并前允许的最大宽度。如果源图片超过此宽度，将被缩小。传 null 或 <=0 表示不限制宽度。
     * @param maxHeightBeforeMerge 合并前允许的最大高度。如果源图片超过此高度，将被缩小。传 null 或 <=0 表示不限制高度。
     * @return 如果合并和保存成功，则返回 true，否则返回 false。
     */
    public static boolean merge(String[] sourceImagePaths, String savePath, Integer maxWidthBeforeMerge,
            Integer maxHeightBeforeMerge) {
        logMemoryUsage("merge:Start");
        // 保存当前 MDC 上下文
        Map<String, String> contextMap = MDCUtil.getCopyOfContextMap();

        try {
            // 设置 MDC 上下文
            MDCUtil.setContextMap(contextMap);

            log.info("开始图片合并操作: 源文件1={}, 源文件2={}, 目标文件={}, 最大宽度={}, 最大高度={}",
                    sourceImagePaths[0], sourceImagePaths[1], savePath, maxWidthBeforeMerge, maxHeightBeforeMerge);

            // 参数校验
            if (sourceImagePaths == null || sourceImagePaths.length != 2 ||
                    sourceImagePaths[0] == null || sourceImagePaths[1] == null ||
                    savePath == null) {
                log.error("合并错误：无效参数。需要两个源路径和一个保存路径。");
                return false;
            }

            // 检查文件是否存在和可读
            File file1 = new File(sourceImagePaths[0]);
            File file2 = new File(sourceImagePaths[1]);
            if (!file1.exists() || !file2.exists() || !file1.canRead() || !file2.canRead()) {
                log.error("合并错误：文件不存在或不可读。路径: {}, {}", sourceImagePaths[0], sourceImagePaths[1]);
                return false;
            }

            // 检查文件大小
            if (file1.length() == 0 || file2.length() == 0) {
                log.error("合并错误：文件大小为0。路径: {}, {}", sourceImagePaths[0], sourceImagePaths[1]);
                return false;
            }

            log.info("源文件验证通过: 文件1大小={}字节, 文件2大小={}字节", file1.length(), file2.length());

            List<String> tempResizedPaths = new ArrayList<>(); // 存储临时调整大小的文件路径
            String[] processedSourcePaths = new String[2]; // 存储最终用于读取的文件路径
            boolean preResizeEnabled = (maxWidthBeforeMerge != null && maxWidthBeforeMerge > 0)
                    || (maxHeightBeforeMerge != null && maxHeightBeforeMerge > 0);
            int effectiveMaxWidth = (maxWidthBeforeMerge != null && maxWidthBeforeMerge > 0) ? maxWidthBeforeMerge
                    : Integer.MAX_VALUE;
            int effectiveMaxHeight = (maxHeightBeforeMerge != null && maxHeightBeforeMerge > 0) ? maxHeightBeforeMerge
                    : Integer.MAX_VALUE;

            log.info("预处理配置: 预调整大小={}, 最大宽度={}, 最大高度={}",
                    preResizeEnabled, effectiveMaxWidth, effectiveMaxHeight);

            // 步骤 0: 预处理 - 检查尺寸并在需要时调整大小
            long preProcessStartTime = System.nanoTime();
            boolean anyResized = false;
            for (int i = 0; i < sourceImagePaths.length; i++) {
                String originalPath = sourceImagePaths[i];
                File sourceFile = new File(originalPath);
                if (!sourceFile.exists()) {
                    log.error("合并错误：源图片未找到: {}", originalPath);
                    return false;
                }

                processedSourcePaths[i] = originalPath;

                if (preResizeEnabled) {
                    try {
                        int[] dimensions = getImageDimensions(sourceFile);
                        int originalWidth = dimensions[0];
                        int originalHeight = dimensions[1];

                        log.info("源图片{}尺寸: {}x{}", i + 1, originalWidth, originalHeight);

                        if (originalWidth > effectiveMaxWidth || originalHeight > effectiveMaxHeight) {
                            log.info("源图片{} ({}x{}) 超出限制 ({}x{})，将进行调整大小",
                                    i + 1, originalWidth, originalHeight, effectiveMaxWidth, effectiveMaxHeight);

                            String originalExtension = getFileExtension(originalPath);
                            if (originalExtension == null) {
                                log.warn("无法确定文件{}的扩展名，跳过预调整大小", originalPath);
                                continue;
                            }

                            String tempResizePath = originalPath.substring(0, originalPath.lastIndexOf("."))
                                    + "_merge_resized." + originalExtension;
                            File tempResizeFile = new File(tempResizePath);

                            try {
                                long resizeStartTime = System.nanoTime();
                                Thumbnails.of(sourceFile)
                                        .size(effectiveMaxWidth, effectiveMaxHeight)
                                        .toFile(tempResizeFile);
                                long resizeEndTime = System.nanoTime();

                                if (tempResizeFile.exists() && tempResizeFile.length() > 0) {
                                    log.info("源图片{}调整大小完成: 原尺寸={}x{}, 新尺寸={}x{}, 耗时={}ms",
                                            i + 1, originalWidth, originalHeight,
                                            getImageDimensions(tempResizeFile)[0],
                                            getImageDimensions(tempResizeFile)[1],
                                            TimeUnit.NANOSECONDS.toMillis(resizeEndTime - resizeStartTime));

                                    processedSourcePaths[i] = tempResizePath;
                                    tempResizedPaths.add(tempResizePath);
                                    anyResized = true;
                                } else {
                                    log.error("调整大小失败，生成了空的临时文件: {}", tempResizePath);
                                    log.warn("将继续使用原始文件{}进行合并", originalPath);
                                }
                            } catch (Exception e) {
                                log.error("调整大小时发生错误: {}", originalPath, e);
                                FileUtil.del(tempResizePath);
                            }
                        } else {
                            log.info("源图片{}尺寸在限制范围内，无需调整", i + 1);
                        }
                    } catch (IOException e) {
                        log.error("读取图片{}尺寸时出错: {}", originalPath, e);
                    }
                }
            }

            if (anyResized) {
                long preProcessEndTime = System.nanoTime();
                log.info("预处理完成，耗时: {}ms",
                        TimeUnit.NANOSECONDS.toMillis(preProcessEndTime - preProcessStartTime));
            }

            // --- 后续合并逻辑使用 processedSourcePaths ---
            File processedFile1 = new File(processedSourcePaths[0]);
            File processedFile2 = new File(processedSourcePaths[1]);

            if (!processedFile1.exists() || !processedFile2.exists()) {
                log.error("处理后的源图片未找到: {}, {}", processedFile1.getPath(), processedFile2.getPath());
                return false;
            }

            String format = getFileExtension(savePath);
            log.info("输出格式: {}", format);
            if (!isFormatSupportedForWriting(format)) {
                log.error("不支持的输出格式: {}", savePath);
                return false;
            }

            BufferedImage image1 = null;
            BufferedImage image2 = null;
            Graphics2D g2d = null;
            File outFile = new File(savePath);
            long totalStartTime = System.nanoTime();
            long readDurationMillis = 0, drawDurationMillis = 0, writeDurationMillis = 0;

            File parentDir = outFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                if (!parentDir.mkdirs()) {
                    log.error("无法创建输出目录: {}", parentDir.getAbsolutePath());
                    return false;
                }
                log.info("已创建输出目录: {}", parentDir.getAbsolutePath());
            }

            try (InputStream is1 = new BufferedInputStream(Files.newInputStream(processedFile1.toPath()));
                    InputStream is2 = new BufferedInputStream(Files.newInputStream(processedFile2.toPath()))) {

                long readStartTime = System.nanoTime();
                image1 = ImageIO.read(is1);
                image2 = ImageIO.read(is2);
                long readEndTime = System.nanoTime();
                readDurationMillis = TimeUnit.NANOSECONDS.toMillis(readEndTime - readStartTime);

                if (image1 == null || image2 == null) {
                    throw new IOException("无法读取源图片");
                }

                int width1 = image1.getWidth();
                int height1 = image1.getHeight();
                int width2 = image2.getWidth();
                int height2 = image2.getHeight();

                log.info("图片读取完成: 图片1={}x{}, 图片2={}x{}, 耗时={}ms",
                        width1, height1, width2, height2, readDurationMillis);

                int maxWidth = Math.max(width1, width2);
                int totalHeight = height1 + height2;

                int imageType = (image1.getTransparency() != Transparency.OPAQUE
                        || image2.getTransparency() != Transparency.OPAQUE)
                                ? BufferedImage.TYPE_INT_ARGB
                                : BufferedImage.TYPE_INT_RGB;

                log.info("创建合并画布: 尺寸={}x{}, 类型={}", maxWidth, totalHeight, imageType);

                logMemoryUsage("merge:BeforeCreateMergedCanvas");
                BufferedImage resultImage = new BufferedImage(maxWidth, totalHeight, imageType);
                logMemoryUsage("merge:AfterCreateMergedCanvas");

                g2d = resultImage.createGraphics();

                long drawStartTime = System.nanoTime();
                g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
                g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                g2d.setBackground(Color.WHITE);
                g2d.clearRect(0, 0, maxWidth, totalHeight);

                int x1 = (maxWidth - width1) / 2;
                g2d.drawImage(image1, x1, 0, null);
                int x2 = (maxWidth - width2) / 2;
                g2d.drawImage(image2, x2, height1, null);

                long drawEndTime = System.nanoTime();
                drawDurationMillis = TimeUnit.NANOSECONDS.toMillis(drawEndTime - drawStartTime);
                log.info("图片合并完成，耗时={}ms", drawDurationMillis);

                long writeStartTime = System.nanoTime();
                logMemoryUsage("merge:BeforeWriteImageWithRetry");
                try (OutputStream os = new BufferedOutputStream(Files.newOutputStream(outFile.toPath()))) {
                    if (!writeImageWithRetry(resultImage, format, outFile)) {
                        throw new IOException("写入图片失败");
                    }
                }
                logMemoryUsage("merge:AfterWriteImageWithRetry");
                long writeEndTime = System.nanoTime();
                writeDurationMillis = TimeUnit.NANOSECONDS.toMillis(writeEndTime - writeStartTime);
                long finalSize = outFile.length();

                long totalEndTime = System.nanoTime();
                long totalDurationMillis = TimeUnit.NANOSECONDS.toMillis(totalEndTime - totalStartTime);

                log.info("合并操作完成: 输出文件={}, 最终尺寸={}x{}, 文件大小={}字节, 总耗时={}ms (读取={}ms, 绘制={}ms, 写入={}ms)",
                        savePath, maxWidth, totalHeight, finalSize, totalDurationMillis,
                        readDurationMillis, drawDurationMillis, writeDurationMillis);

                logMemoryUsage("merge:SuccessReturnTrue");
                return true;

            } catch (IOException e) {
                log.error("文件IO错误: {}, {}: {}", processedFile1.getPath(), processedFile2.getPath(), savePath, e);
                logMemoryUsage("merge:IOExceptionReturnFalse");
                return false;
            } catch (Exception e) {
                log.error("发生意外错误: {}, {}: {}", processedFile1.getPath(), processedFile2.getPath(), savePath, e);
                logMemoryUsage("merge:ExceptionReturnFalse");
                return false;
            } finally {
                if (totalStartTime > 0 && g2d == null) {
                    long totalEndTime = System.nanoTime();
                    long totalDurationMillis = TimeUnit.NANOSECONDS.toMillis(totalEndTime - totalStartTime);
                    log.error("操作提前终止，已耗时: {}ms", totalDurationMillis);
                }

                if (g2d != null) {
                    g2d.dispose();
                    log.info("已释放Graphics2D资源");
                }

                image1 = null;
                image2 = null;

                for (String tempPath : tempResizedPaths) {
                    try {
                        FileUtil.del(tempPath);
                        log.info("已清理临时文件: {}", tempPath);
                    } catch (Exception e) {
                        log.warn("清理临时文件失败: {}", tempPath, e);
                    }
                }
            }
        } catch (Exception e) {
            log.error("合并过程发生错误: {}, {}: {}", sourceImagePaths[0], sourceImagePaths[1], savePath, e);
            logMemoryUsage("merge:OuterCatchReturnFalse");
            return false;
        } finally {
            MDCUtil.clear();
            logMemoryUsage("merge:OuterFinallyEnd");
        }
    }

    private static boolean validateImageFormat(String imagePath) {
        try {
            File file = new File(imagePath);
            if (!file.exists()) {
                return false;
            }

            // 使用 ImageIO 读取图片信息
            try (ImageInputStream iis = ImageIO.createImageInputStream(file)) {
                Iterator<ImageReader> readers = ImageIO.getImageReaders(iis);
                if (!readers.hasNext()) {
                    return false;
                }
                ImageReader reader = readers.next();
                reader.setInput(iis);

                // 检查图片尺寸
                int width = reader.getWidth(0);
                int height = reader.getHeight(0);

                // 检查尺寸是否为0或负数
                if (width <= 0 || height <= 0) {
                    log.error("验证图片格式失败: 图片尺寸无效 ({}x{}): {}", width, height, imagePath);
                    return false;
                }

                // 检查尺寸是否过大
                if (width > 65535 || height > 65535) {
                    log.error("验证图片格式失败: 图片尺寸过大 ({}x{}): {}", width, height, imagePath);
                    return false;
                }

                // 检查颜色空间
                int numComponents = reader.getRawImageType(0).getNumComponents();
                if (numComponents != 1 && numComponents != IMAGE_WITH_REAR_MAX && numComponents != 4) {
                    log.error("验证图片格式失败: 不支持的颜色空间 (components: {}): {}", numComponents, imagePath);
                    return false;
                }

                return true;
            }
        } catch (Exception e) {
            log.error("验证图片格式失败: {}", imagePath, e);
            return false;
        }
    }

    // 重载旧的 merge 方法以保持向后兼容性，默认不进行预调整
    /**
     * @deprecated 请使用 merge(String[], String, Integer, Integer) 以利用可选的预调整大小功能。
     *             保留此方法以实现向后兼容。
     */
    @Deprecated
    public static boolean merge(String[] sourceImagePaths, String savePath) {
        log.warn("调用了已废弃的 merge(String[], String) 方法。建议使用包含预调整大小参数的版本。");
        return merge(sourceImagePaths, savePath, null, null); // 调用新方法，但不启用预调整
    }

    // --- 辅助方法 ---

    /**
     * 高效地获取图片文件的尺寸（宽度和高度），而不加载整个图片数据。
     *
     * @param imageFile 图片文件对象。
     * @return 包含 [宽度, 高度] 的整数数组。
     * @throws IOException 如果读取尺寸信息时发生错误或文件不是有效的图片格式。
     */
    private static int[] getImageDimensions(File imageFile) throws IOException {
        try (ImageInputStream iis = ImageIO.createImageInputStream(imageFile)) {
            if (iis == null) {
                throw new IOException("无法为文件创建 ImageInputStream: " + imageFile.getPath());
            }
            Iterator<ImageReader> readers = ImageIO.getImageReaders(iis);
            if (readers.hasNext()) {
                ImageReader reader = readers.next();
                try {
                    reader.setInput(iis);
                    int width = reader.getWidth(reader.getMinIndex());
                    int height = reader.getHeight(reader.getMinIndex());
                    return new int[] { width, height };
                } finally {
                    reader.dispose(); // 确保 reader 资源被释放
                }
            } else {
                throw new IOException("未找到适合文件 " + imageFile.getPath() + " 的 ImageReader");
            }
        }
    }

    /**
     * 获取文件路径的扩展名（小写）。
     * 
     * @param filePath 文件路径。
     * @return 文件扩展名（不含点），如果找不到则返回 null。
     */
    private static String getFileExtension(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return null; // 未找到扩展名
        }
        int lastDot = filePath.lastIndexOf('.');
        // 检查点是否存在且不是最后一个字符
        if (lastDot == -1 || lastDot == filePath.length() - 1) {
            return null; // 未找到扩展名
        }
        return filePath.substring(lastDot + 1).toLowerCase(); // 返回点之后的部分并转为小写
    }

    /**
     * 检查 ImageIO 是否支持写入指定的图片格式。
     * 
     * @param format 图片格式（例如 "jpg", "png"）。
     * @return 如果支持则返回 true，否则返回 false。
     */
    private static boolean isFormatSupportedForWriting(String format) {
        if (format == null)
            return false;
        // 获取 ImageIO 支持写入的所有格式名称
        String[] supportedFormats = ImageIO.getWriterFormatNames();
        // 检查给定格式（忽略大小写）是否存在于支持的格式列表中
        return Arrays.stream(supportedFormats).anyMatch(f -> f.equalsIgnoreCase(format));
    }

    // --- 图片合并逻辑 ---

    /**
     * 将图片压缩以适应目标大小和尺寸。
     * 如果需要，首先调整大小，然后调整质量。
     *
     * @param imagePath       源图片路径。
     * @param targetSizeBytes 目标文件大小（字节）。
     * @param maxWidth        最大允许宽度。
     * @param maxHeight       最大允许高度。
     * @return 处理后的图片路径，如果无需处理/无法处理则返回原始路径，或在错误时返回 null。
     */
    public static String compressAndResize(String imagePath, long targetSizeBytes, int maxWidth, int maxHeight) {
        logMemoryUsage("compressAndResize:Start");
        File inputFile = new File(imagePath);
        // 检查输入文件
        if (!inputFile.exists() || !inputFile.isFile() || !inputFile.canRead()) {
            log.error("compressAndResize错误：输入文件未找到或不可读: {}", imagePath);
            return null;
        }

        long originalSize = inputFile.length();
        long startTime = System.nanoTime();
        String originalExtension = getFileExtension(imagePath);
        if (originalExtension == null) {
            log.error("compressAndResize错误：无法确定文件扩展名: {}", imagePath);
            return null;
        }

        boolean needsResize = false;
        boolean needsCompress = false; // 将在可能的调整大小后确定
        String tempResizePath = null;
        String currentImagePath = imagePath;
        long currentFileSize = originalSize;

        try {
            logMemoryUsage("compressAndResize:BeforeReadOriginal");
            BufferedImage originalImage = ImageIO.read(inputFile);
            logMemoryUsage("compressAndResize:AfterReadOriginal");
            if (originalImage == null) {
                log.error("compressAndResize错误：读取图片失败 {}", imagePath);
                logMemoryUsage("compressAndResize:ReadFailReturnNull");
                return null; // 表示读取失败
            }
            int originalWidth = originalImage.getWidth();
            int originalHeight = originalImage.getHeight();
            originalImage = null; // 释放内存

            if (originalWidth > maxWidth || originalHeight > maxHeight) {
                needsResize = true;
                log.info("图片 {} ({}x{}) 超出尺寸限制 ({}x{}), 正在调整大小。",
                        imagePath, originalWidth, originalHeight, maxWidth, maxHeight);

                tempResizePath = imagePath.substring(0, imagePath.lastIndexOf(".")) + "_resized." + originalExtension;
                File tempResizeFile = new File(tempResizePath);

                try {
                    logMemoryUsage("compressAndResize:BeforeResize");
                    Thumbnails.of(inputFile)
                            .size(maxWidth, maxHeight) // 在保持宽高比的同时调整大小
                            .toFile(tempResizeFile);
                    logMemoryUsage("compressAndResize:AfterResize");
                } catch (UnsupportedFormatException ufe) {
                    log.error("compressAndResize错误：调整大小时不支持的格式 {}", imagePath, ufe);
                    logMemoryUsage("compressAndResize:ResizeFailReturnNull1");
                    return null; // 无法调整此格式的大小
                } catch (IOException ioe) {
                    log.error("compressAndResize错误：调整大小时发生IO错误 {}", imagePath, ioe);
                    logMemoryUsage("compressAndResize:ResizeFailReturnNull2");
                    FileUtil.del(tempResizePath); // 清理可能不完整的文件
                    return null;
                }

                if (!tempResizeFile.exists() || tempResizeFile.length() == 0) {
                    log.error("compressAndResize错误：无法创建或生成了空的已调整大小文件于 {}", tempResizePath);
                    FileUtil.del(tempResizePath);
                    logMemoryUsage("compressAndResize:ResizeFailReturnNull3");
                    return null; // 调整大小失败
                }

                currentImagePath = tempResizePath;
                currentFileSize = tempResizeFile.length();
                log.info("已将图片 {} 调整大小至 {}, 当前大小 {} 字节。", imagePath, tempResizePath, currentFileSize);
            }

            // 步骤 2: 在可能的调整大小后确定是否需要压缩
            needsCompress = currentFileSize > targetSizeBytes;

            if (needsCompress) {
                log.info("图片 {} (大小 {} 字节) 需要压缩至目标 {} 字节。",
                        currentImagePath, currentFileSize, targetSizeBytes);

                String finalCompressedPath = currentImagePath.substring(0, currentImagePath.lastIndexOf("."))
                        + "_final." + getFileExtension(currentImagePath);
                File finalCompressedFile = new File(finalCompressedPath);

                try (InputStream is = new BufferedInputStream(Files.newInputStream(Paths.get(currentImagePath)));
                        OutputStream os = new BufferedOutputStream(
                                Files.newOutputStream(finalCompressedFile.toPath()))) {

                    logMemoryUsage("compressAndResize:BeforeCompressStream");
                    boolean qualityCompressionSuccess = compressStreamToTargetSize(is, os, targetSizeBytes,
                            currentImagePath);
                    logMemoryUsage("compressAndResize:AfterCompressStream");

                    long endTime = System.nanoTime();
                    long durationMillis = TimeUnit.NANOSECONDS.toMillis(endTime - startTime);

                    if (qualityCompressionSuccess) {
                        long finalSize = finalCompressedFile.length();
                        log.info("compressAndResize成功 (调整大小={}, 压缩=true): 原始路径 {}, 最终路径 {}, 最终大小 {} 字节。总耗时: {} ms",
                                needsResize, imagePath, finalCompressedPath, finalSize, durationMillis);

                        // 如果存在中间调整大小的文件且与最终文件不同，则清理它
                        if (tempResizePath != null && !tempResizePath.equals(finalCompressedPath)) {
                            FileUtil.del(tempResizePath);
                        }
                        logMemoryUsage("compressAndResize:CompressSuccessReturnPath");
                        return finalCompressedPath;
                    } else {
                        log.warn("compressAndResize警告 (调整大小={}, 压缩=失败): 对 {} 的质量压缩失败。总耗时: {} ms",
                                needsResize, currentImagePath, durationMillis);
                        FileUtil.del(finalCompressedPath); // 清理失败的压缩尝试

                        // 如果进行了尺寸调整，返回中间调整后的路径，否则返回原始路径
                        if (tempResizePath != null) {
                            log.warn("返回中间调整大小后的文件: {}", tempResizePath);
                            return tempResizePath;
                        } else {
                            log.warn("因压缩失败且未进行尺寸调整，返回原始文件: {}", imagePath);
                            return imagePath;
                        }
                    }
                } catch (Exception eInner) {
                    log.error("compressAndResize 内部压缩流处理错误: {}", eInner.getMessage(), eInner);
                    logMemoryUsage("compressAndResize:CompressStreamException");
                    // 根据情况返回原始或调整后的路径，或null
                    if (tempResizePath != null)
                        return tempResizePath;
                    else
                        return imagePath;
                }
            } else {
                // 无需质量压缩 (原始文件足够小或调整大小后足够小)
                long endTime = System.nanoTime();
                long durationMillis = TimeUnit.NANOSECONDS.toMillis(endTime - startTime);
                log.info("compressAndResize完成 (调整大小={}, 压缩=false): 原始路径 {}, 最终路径 {}, 最终大小 {} 字节。总耗时: {} ms",
                        needsResize, imagePath, currentImagePath, currentFileSize, durationMillis);
                // 返回当前路径 (原始路径或调整大小后的临时路径)
                logMemoryUsage("compressAndResize:NoCompressReturnPath");
                return currentImagePath;
            }

        } catch (IOException e) {
            log.error("compressAndResize对 {} 发生IO错误: ", imagePath, e);
            if (tempResizePath != null) {
                FileUtil.del(tempResizePath);
            }
            logMemoryUsage("compressAndResize:IOExceptionReturnNull");
            return null; // 表示错误
        } catch (Exception e) {
            log.error("compressAndResize对 {} 发生意外错误: ", imagePath, e);
            if (tempResizePath != null) {
                FileUtil.del(tempResizePath);
            }
            logMemoryUsage("compressAndResize:ExceptionReturnNull");
            return null; // 表示错误
        } finally {
            logMemoryUsage("compressAndResize:FinallyEnd");
        }
    }

    /**
     * 核心压缩逻辑：使用 BufferedImage 输入和基于质量的二分查找。
     * 尝试将 BufferedImage 压缩到 targetSizeBytes 以下，并将结果作为字节数组返回。
     *
     * @param image           源 BufferedImage。
     * @param targetSizeBytes 目标大小（字节）。
     * @param outputFormat    输出格式 (例如 "jpg", "png")。
     * @param imageId         用于日志记录的标识符。
     * @return 压缩后的字节数组，如果无法达到目标大小或发生错误则返回 null。
     */
    private static byte[] compressBufferedImageToTargetSize(BufferedImage image, long targetSizeBytes,
            String outputFormat, String imageId) {
        if (image == null) {
            log.error("compressBufferedImageToTargetSize 错误: 输入的 BufferedImage 为 null, imageId: {}", imageId);
            return null;
        }
        if (!isFormatSupportedForWriting(outputFormat)) {
            log.error("compressBufferedImageToTargetSize 错误: 不支持的输出格式 '{}', imageId: {}", outputFormat, imageId);
            return null;
        }

        long initialSizeEstimate = estimateBufferedImageSize(image, outputFormat); // 估算初始大小
        log.debug("开始 BufferedImage 压缩: imageId={}, 目标大小={}, 估算初始大小≈{}, 输出格式={}",
                imageId, targetSizeBytes, initialSizeEstimate, outputFormat);
        // 简单的预检查：如果估算大小已经远小于目标，可以考虑直接高质量输出或跳过复杂查找 (此处未实现)

        double minQuality = 0.0;
        double maxQuality = 1.0;
        byte[] bestCompressedBytes = null;
        int iterations = 0;
        long searchStartTime = System.nanoTime();

        for (int i = 0; i < COMPRESSION_ITERATION_LIMIT; i++) {
            iterations++;
            double currentQuality = (minQuality + maxQuality) / 2.0;
            if (currentQuality < 0.01)
                currentQuality = 0.01;
            if (Math.abs(maxQuality - minQuality) < 0.01)
                break;

            ByteArrayOutputStream tempOutputStream = new ByteArrayOutputStream();
            try {
                // 使用当前质量尝试压缩 BufferedImage
                Thumbnails.of(image)
                        .scale(1.0) // 保持尺寸
                        .outputQuality(currentQuality)
                        .outputFormat(outputFormat)
                        .toOutputStream(tempOutputStream);

                byte[] compressedBytes = tempOutputStream.toByteArray();
                long currentSize = compressedBytes.length;
                log.debug("BufferedImage 压缩迭代 {}: imageId={}, 质量={:.3f}, 大小={}字节",
                        i + 1, imageId, currentQuality, currentSize);

                if (currentSize <= targetSizeBytes) {
                    bestCompressedBytes = compressedBytes;
                    minQuality = currentQuality; // 尝试更高质量
                } else {
                    maxQuality = currentQuality; // 需要更低质量
                }
            } catch (UnsupportedFormatException e) {
                log.error("BufferedImage 压缩失败: imageId={}, 不支持的格式 {}", imageId, outputFormat, e);
                return null; // 格式问题，无法继续
            } catch (IOException e) {
                log.warn("BufferedImage 压缩迭代IO异常: imageId={}, quality={:.3f}", imageId, currentQuality, e);
                // 可以选择停止或继续，这里选择停止以避免重复错误
                return null;
            } catch (Exception e) { // 捕获其他潜在错误 (例如 NullPointerException in Thumbnails)
                log.error("BufferedImage 压缩迭代时发生意外错误: imageId={}, quality={:.3f}", imageId, currentQuality, e);
                return null;
            } finally {
                IoUtil.close(tempOutputStream); // 确保流关闭
            }
            // 防止在最低质量仍然失败时无限循环
            if (currentQuality <= 0.01 && bestCompressedBytes == null) {
                log.warn("BufferedImage 压缩警告: imageId={}, 最低质量 0.01 尝试后仍未找到低于目标大小 {} 的结果。",
                        imageId, targetSizeBytes);
                break;
            }
        }

        long searchEndTime = System.nanoTime();
        long searchDurationMillis = TimeUnit.NANOSECONDS.toMillis(searchEndTime - searchStartTime);

        if (bestCompressedBytes != null) {
            log.info("BufferedImage 压缩完成: imageId={}, 找到质量≈{:.3f} ({}次迭代), 最终大小={}字节, 耗时={}ms",
                    imageId, minQuality, iterations, bestCompressedBytes.length, searchDurationMillis);
            return bestCompressedBytes;
        } else {
            // 检查最低质量是否尝试过，并且确实失败了
            ByteArrayOutputStream checkLowestQualityStream = new ByteArrayOutputStream();
            long lastAttemptSize = -1;
            try {
                Thumbnails.of(image)
                        .scale(1.0) // 保持尺寸
                        .outputQuality(0.01) // 尝试最低质量
                        .outputFormat(outputFormat)
                        .toOutputStream(checkLowestQualityStream);
                lastAttemptSize = checkLowestQualityStream.toByteArray().length;
            } catch (Exception e) {
                log.warn("检查最低质量时发生错误, imageId={}: {}", imageId, e.getMessage());
            } finally {
                IoUtil.close(checkLowestQualityStream);
            }

            log.warn("BufferedImage 压缩失败: imageId={}, 无法在{}次迭代内将大小降至{}字节以下。查找耗时={}ms. 最低质量(0.01)尝试大小: {}",
                    imageId, iterations, targetSizeBytes, searchDurationMillis,
                    lastAttemptSize > 0 ? lastAttemptSize + "字节" : "(未成功获取或失败)");
            // 即使压缩失败，如果原始估算大小就小于目标，是否应该尝试输出高质量原始图像？(此处未实现)
            return null;
        }
    }

    /**
     * 估算 BufferedImage 序列化后的大致大小 (非常粗略)。
     * 实际大小受压缩算法和格式影响很大。
     * 
     * @param image  BufferedImage 对象。
     * @param format 输出格式。
     * @return 估算的字节数。
     */
    private static long estimateBufferedImageSize(BufferedImage image, String format) {
        if (image == null)
            return 0;
        int bitsPerPixel = 24; // 默认 RGB
        if (image.getType() == BufferedImage.TYPE_INT_ARGB || image.getType() == BufferedImage.TYPE_4BYTE_ABGR) {
            bitsPerPixel = 32;
        } else if (image.getType() == BufferedImage.TYPE_BYTE_GRAY
                || image.getType() == BufferedImage.TYPE_USHORT_GRAY) {
            bitsPerPixel = 8; // 或 16，简化为 8
        }
        // 简单估算：宽度 * 高度 * 每像素位数 / 8
        // 对于 JPEG 等有损压缩，这个估算会严重偏大
        return (long) image.getWidth() * image.getHeight() * bitsPerPixel / 8;
    }

    /**
     * 【内存优化版】【并行读取】合并、调整大小并压缩两张图片到字节数组。
     * 整个过程尽可能在内存中操作，减少磁盘 I/O。 读取和缩放步骤并行执行。
     *
     * @param sourceImagePaths 包含两个源图片路径的数组。
     * @param targetMaxWidth   最终合并图像的目标最大宽度（用于预缩放和最终尺寸）。
     * @param targetMaxHeight  最终合并图像的目标最大高度（用于预缩放和最终尺寸）。
     * @param targetSizeBytes  最终输出字节数组的目标最大大小。
     * @param outputFormat     最终输出格式 (例如 "jpg")。
     * @param executor         用于并行执行读取/缩放任务的 Executor。
     * @return 处理并压缩后的字节数组，如果操作失败则返回 null。
     */
    public static byte[] mergeResizeCompressToByteArray(String[] sourceImagePaths, int targetMaxWidth,
            int targetMaxHeight, long targetSizeBytes, String outputFormat, Executor executor) {

        logMemoryUsage("mergeResizeCompressToByteArray:Start");
        long wholeProcessStart = System.nanoTime(); // Re-added for overall timing log, can be removed if not needed.

        // 1. 参数校验 (executor added to checks)
        if (sourceImagePaths == null || sourceImagePaths.length != 2 || sourceImagePaths[0] == null
                || sourceImagePaths[1] == null) {
            log.error("mergeResizeCompressToByteArray(Parallel) 错误：需要两个有效的源图片路径。{}", Arrays.toString(sourceImagePaths));
            return null;
        }
        if (!isFormatSupportedForWriting(outputFormat)) {
            log.error("mergeResizeCompressToByteArray(Parallel) 错误: 不支持的输出格式 '{}'", outputFormat);
            return null;
        }
        if (targetMaxWidth <= 0 || targetMaxHeight <= 0 || targetSizeBytes <= 0) {
            log.error("mergeResizeCompressToByteArray(Parallel) 错误: 目标尺寸和大小必须为正数。W={}, H={}, Size={}",
                    targetMaxWidth, targetMaxHeight, targetSizeBytes);
            return null;
        }
        if (executor == null) {
            log.error("mergeResizeCompressToByteArray(Parallel) 错误: Executor 不能为空。");
            return null;
        }

        BufferedImage sourceImage1 = null;
        BufferedImage sourceImage2 = null;
        BufferedImage mergedCanvas = null;
        Graphics2D g2d = null;
        byte[] finalBytes = null;
        long readAndResizeDuration = 0;
        long mergeDuration = 0;
        long compressDuration = 0;

        try {
            // 2. 并行读取和预缩放源图片到内存 BufferedImage
            long readResizeStart = System.nanoTime();

            // 使用 CompletableFuture 并行执行
            CompletableFuture<BufferedImage> future1 = CompletableFuture.supplyAsync(() -> {
                // Lambda logic for the first image (index 0)
                String path = sourceImagePaths[0];
                File file = new File(path);
                if (!file.exists() || !file.canRead()) {
                    log.error("内存处理(Parallel): 源文件不存在或不可读: {}", path);
                    return null;
                }
                try {
                    int[] dimensions = getImageDimensions(file);
                    int originalWidth = dimensions[0];
                    int originalHeight = dimensions[1];
                    BufferedImage resultImage;
                    if (originalWidth > targetMaxWidth || originalHeight > targetMaxHeight) {
                        log.info("内存处理(Parallel): 源图片 {} ({}x{}) 超出目标尺寸({}x{})，将直接读取并调整大小。",
                                path, originalWidth, originalHeight, targetMaxWidth, targetMaxHeight);
                        resultImage = Thumbnails.of(file).size(targetMaxWidth, targetMaxHeight).asBufferedImage();
                    } else {
                        log.info("内存处理(Parallel): 源图片 {} ({}x{}) 未超出目标尺寸，直接读取。",
                                path, originalWidth, originalHeight);
                        resultImage = ImageIO.read(file);
                    }
                    if (resultImage == null) {
                        log.error("内存处理(Parallel): 无法将文件解码为 BufferedImage: {}", path);
                        return null;
                    }
                    return resultImage;
                } catch (IOException e) {
                    log.error("内存处理(Parallel): 读取或调整源图片 {} 时发生 IO 错误。", path, e);
                    return null;
                } catch (Exception e) {
                    log.error("内存处理(Parallel): 处理源图片 {} 时发生意外错误。", path, e);
                    return null;
                }
            }, executor);

            CompletableFuture<BufferedImage> future2 = CompletableFuture.supplyAsync(() -> {
                // Lambda logic for the second image (index 1)
                String path = sourceImagePaths[1];
                File file = new File(path);
                if (!file.exists() || !file.canRead()) {
                    log.error("内存处理(Parallel): 源文件不存在或不可读: {}", path);
                    return null;
                }
                try {
                    int[] dimensions = getImageDimensions(file);
                    int originalWidth = dimensions[0];
                    int originalHeight = dimensions[1];
                    BufferedImage resultImage;
                    if (originalWidth > targetMaxWidth || originalHeight > targetMaxHeight) {
                        log.info("内存处理(Parallel): 源图片 {} ({}x{}) 超出目标尺寸({}x{})，将直接读取并调整大小。",
                                path, originalWidth, originalHeight, targetMaxWidth, targetMaxHeight);
                        resultImage = Thumbnails.of(file).size(targetMaxWidth, targetMaxHeight).asBufferedImage();
                    } else {
                        log.info("内存处理(Parallel): 源图片 {} ({}x{}) 未超出目标尺寸，直接读取。",
                                path, originalWidth, originalHeight);
                        resultImage = ImageIO.read(file);
                    }
                    if (resultImage == null) {
                        log.error("内存处理(Parallel): 无法将文件解码为 BufferedImage: {}", path);
                        return null;
                    }
                    return resultImage;
                } catch (IOException e) {
                    log.error("内存处理(Parallel): 读取或调整源图片 {} 时发生 IO 错误。", path, e);
                    return null;
                } catch (Exception e) {
                    log.error("内存处理(Parallel): 处理源图片 {} 时发生意外错误。", path, e);
                    return null;
                }
            }, executor);

            // 等待两个任务完成
            CompletableFuture.allOf(future1, future2).join(); // join 会在异常时抛出 CompletionException

            // 获取结果 (如果 join 成功，get 不会阻塞)
            // get() 会抛出 InterruptedException 和 ExecutionException，后者包装了原始异常
            try {
                sourceImage1 = future1.get();
                sourceImage2 = future2.get();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt(); // 重置中断状态
                log.error("内存处理(Parallel): 获取图片结果时线程被中断。", e);
                return null;
            } catch (ExecutionException e) {
                log.error("内存处理(Parallel): 读取/缩放任务执行时发生错误。", e.getCause() != null ? e.getCause() : e);
                return null;
            }

            long readResizeEnd = System.nanoTime();
            readAndResizeDuration = TimeUnit.NANOSECONDS.toMillis(readResizeEnd - readResizeStart);

            // 检查并行任务是否成功返回了 BufferedImage
            if (sourceImage1 == null || sourceImage2 == null) {
                log.error("内存处理(Parallel): 一个或两个图片未能成功读取或调整大小。Source1 null: {}, Source2 null: {}",
                        sourceImage1 == null, sourceImage2 == null);
                return null;
            }
            log.info("内存处理(Parallel)：并行读取和预调整大小完成。耗时: {} ms", readAndResizeDuration);

            // 3. 合并 BufferedImage 到内存画布
            long mergeStart = System.nanoTime();
            int width1 = sourceImage1.getWidth();
            int height1 = sourceImage1.getHeight();
            int width2 = sourceImage2.getWidth();
            int height2 = sourceImage2.getHeight();
            int mergedWidth = Math.max(width1, width2);
            int mergedHeight = height1 + height2;

            // 创建合并画布
            int imageType = (sourceImage1.getTransparency() != Transparency.OPAQUE
                    || sourceImage2.getTransparency() != Transparency.OPAQUE)
                            ? BufferedImage.TYPE_INT_ARGB
                            : BufferedImage.TYPE_INT_RGB;
            logMemoryUsage("mergeResizeCompressToByteArray:BeforeCreateMergedCanvas");
            mergedCanvas = new BufferedImage(mergedWidth, mergedHeight, imageType);
            logMemoryUsage("mergeResizeCompressToByteArray:AfterCreateMergedCanvas");
            g2d = mergedCanvas.createGraphics();

            // 设置渲染提示和背景
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setBackground(Color.WHITE);
            g2d.clearRect(0, 0, mergedWidth, mergedHeight);

            // 居中绘制
            int x1 = (mergedWidth - width1) / 2;
            g2d.drawImage(sourceImage1, x1, 0, null);
            int x2 = (mergedWidth - width2) / 2;
            g2d.drawImage(sourceImage2, x2, height1, null);

            long mergeEnd = System.nanoTime();
            mergeDuration = TimeUnit.NANOSECONDS.toMillis(mergeEnd - mergeStart);
            log.info("内存处理：BufferedImage 合并完成。尺寸: {}x{}, 耗时: {} ms", mergedWidth, mergedHeight, mergeDuration);

            // 及时释放不再需要的源图像内存
            sourceImage1 = null;
            sourceImage2 = null;
            // System.gc(); // 不建议显式调用

            // 4. 压缩合并后的 BufferedImage 到目标大小的字节数组
            if (mergedCanvas == null) { // 双重检查
                log.error("内存处理：合并后的 BufferedImage 为 null");
                return null;
            }
            long compressStart = System.nanoTime();
            logMemoryUsage("mergeResizeCompressToByteArray:BeforeCompressBufferedImage");
            finalBytes = compressBufferedImageToTargetSize(mergedCanvas, targetSizeBytes, outputFormat, "merged_image");
            logMemoryUsage("mergeResizeCompressToByteArray:AfterCompressBufferedImage");
            long compressEnd = System.nanoTime();
            compressDuration = TimeUnit.NANOSECONDS.toMillis(compressEnd - compressStart);

            // 及时释放合并画布内存
            mergedCanvas = null;

            if (finalBytes == null) {
                log.error("内存处理：最终压缩到目标大小 {} 字节失败。", targetSizeBytes);
                return null; // 压缩失败
            }
            log.info("内存处理：最终压缩完成。大小: {} 字节, 耗时: {} ms", finalBytes.length, compressDuration);

        } catch (OutOfMemoryError oom) {
            // 关键：捕获 OOM
            log.error("内存处理：严重错误: 内存不足！", oom);
            logMemoryUsage("mergeResizeCompressToByteArray:OOMReturnNull");
            // 尝试释放可能持有的对象
            sourceImage1 = null;
            sourceImage2 = null;
            mergedCanvas = null;
            finalBytes = null;
            // System.gc(); // 在 OOM 后调用 GC 可能帮助不大，但可以尝试
            return null;
        } catch (Throwable t) { // 捕获所有其他潜在错误
            log.error("内存处理：发生意外错误。", t);
            logMemoryUsage("mergeResizeCompressToByteArray:ThrowableReturnNull");
            return null;
        } finally {
            // 5. 清理资源 (确保 Graphics2D 被释放)
            if (g2d != null) {
                g2d.dispose();
            }
            // 确保 BufferedImage 引用被清除 (在 catch 块中也处理了)
            sourceImage1 = null;
            sourceImage2 = null;
            mergedCanvas = null;

            // long wholeProcessEnd = System.nanoTime(); // Re-added for overall timing log.
            // Removed the log line that caused the error
            // log.info("内存处理：总耗时: {} ms, 读缩放: {} ms, 合并绘制: {} ms, 压缩: {} ms",
            // TimeUnit.NANOSECONDS.toMillis(wholeProcessEnd - wholeProcessStart), // This
            // line used wholeProcessStart
            // readAndResizeDuration, mergeDuration, compressDuration);
            logMemoryUsage("mergeResizeCompressToByteArray:FinallyEnd");
        }

        return finalBytes;
    }

    /**
     * 检查图片尺寸是否为偶数
     */
    private static boolean isEvenSize(BufferedImage image) {
        return image.getWidth() % 2 == 0 && image.getHeight() % 2 == 0;
    }

    /**
     * 确保图片尺寸为偶数，如果不是则调整
     */
    private static BufferedImage ensureEvenSize(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();

        // 如果宽度和高度都是偶数，直接返回原图
        if (isEvenSize(image)) {
            return image;
        }

        // 调整到偶数尺寸
        int newWidth = width + (width % 2);
        int newHeight = height + (height % 2);

        // 创建新的图片
        BufferedImage newImage = new BufferedImage(newWidth, newHeight, image.getType());
        Graphics2D g2d = newImage.createGraphics();
        try {
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.setBackground(Color.WHITE);
            g2d.clearRect(0, 0, newWidth, newHeight);
            g2d.drawImage(image, 0, 0, null);
            return newImage;
        } finally {
            g2d.dispose();
        }
    }

    /**
     * 带重试机制的图片写入
     */
    private static boolean writeImageWithRetry(BufferedImage image, String format, File outFile) {
        logMemoryUsage("writeImageWithRetry:Start");
        int retryCount = 0;
        long waitTime = 100; // 初始等待时间（毫秒）
        String filePath = outFile.getAbsolutePath(); // 在循环外获取路径以供日志记录

        while (retryCount <= IMAGE_WITH_REAR_MAX) {
            OutputStream os = null; // 将 os 声明移到 try 外部，以便在 catch/finally 中也能访问
            boolean success;
            try {
                os = new BufferedOutputStream(Files.newOutputStream(outFile.toPath()));
                // 确保图片尺寸为偶数
                BufferedImage finalImage = ensureEvenSize(image);

                // 如果是JPEG格式，确保使用RGB颜色空间
                if ("jpg".equalsIgnoreCase(format) || "jpeg".equalsIgnoreCase(format)) {
                    if (finalImage.getType() != BufferedImage.TYPE_INT_RGB) {
                        log.info("writeImageWithRetry: 原始图片类型非RGB ({})，转换为RGB进行写入。文件: {}", finalImage.getType(),
                                outFile.getName());
                        BufferedImage rgbImage = new BufferedImage(
                                finalImage.getWidth(),
                                finalImage.getHeight(),
                                BufferedImage.TYPE_INT_RGB);
                        Graphics2D g2d = rgbImage.createGraphics();
                        try {
                            g2d.setBackground(Color.WHITE);
                            g2d.clearRect(0, 0, rgbImage.getWidth(), rgbImage.getHeight());
                            g2d.drawImage(finalImage, 0, 0, null);
                            finalImage = rgbImage; // 使用转换后的RGB图像
                        } finally {
                            g2d.dispose();
                        }
                    } else {
                        log.info("writeImageWithRetry: 原始图片类型已是RGB ({})。文件: {}", finalImage.getType(),
                                outFile.getName());
                    }
                }

                // 在写入前记录详细信息
                String colorModelInfo = (finalImage.getColorModel() != null) ? finalImage.getColorModel().toString()
                        : "null";
                log.info("准备写入图片: 路径='{}', 格式='{}', 宽度={}, 高度={}, 类型={}, 颜色模型='{}'",
                        filePath, format, finalImage.getWidth(), finalImage.getHeight(),
                        finalImage.getType(), colorModelInfo);

                // 写入图片
                logMemoryUsage("writeImageWithRetry:BeforeWriteAttempt" + retryCount);
                success = ImageIO.write(finalImage, format, os);
                logMemoryUsage("writeImageWithRetry:AfterWriteAttempt" + retryCount);
                os.flush(); // 确保所有数据写入

                if (!success) {
                    // 如果 ImageIO.write 返回 false 但未抛出异常
                    throw new IOException("ImageIO.write 返回 false，格式不支持或写入器内部错误: " + format);
                }

                log.info("成功写入图片: {}", filePath);
                logMemoryUsage("writeImageWithRetry:SuccessReturnTrue");
                return true; // 成功写入，跳出循环

            } catch (IOException e) {
                // IO 异常处理
                retryCount++;
                String errorMessage = (e.getMessage() != null) ? e.getMessage() : e.getClass().getSimpleName();
                if (retryCount <= IMAGE_WITH_REAR_MAX) {
                    log.warn("写入图片失败 (路径: {}), 正在重试 ({}/{}): {}", filePath, retryCount,
                            IMAGE_WITH_REAR_MAX, errorMessage);
                    logMemoryUsage("writeImageWithRetry:IOExceptionRetry" + retryCount);
                    try {
                        // 重试前删除可能不完整的文件
                        FileUtil.del(outFile);
                        Thread.sleep(waitTime);
                        waitTime *= 2; // 指数退避
                    } catch (InterruptedException ie) {
                        log.warn("写入图片重试等待时被中断: {}", filePath);
                        Thread.currentThread().interrupt();
                        return false; // 中断，不再继续
                    }

                } else {
                    log.error("写入图片失败 (路径: {}), 已达到最大重试次数({}): {}", filePath, IMAGE_WITH_REAR_MAX, errorMessage, e);
                    FileUtil.del(outFile); // 最终失败后也删除文件
                    logMemoryUsage("writeImageWithRetry:IOExceptionMaxRetriesReturnFalse");
                    return false; // 达到最大重试次数
                }
            } catch (Exception e) {
                // 捕获其他潜在的运行时异常 (例如 NullPointerException)
                log.error("写入图片时发生意外错误 (路径: {}): {}", filePath, e.getMessage(), e);
                FileUtil.del(outFile); // 发生意外错误后删除文件
                logMemoryUsage("writeImageWithRetry:ExceptionReturnFalse");
                return false; // 发生意外错误，不再重试
            } finally {
                // 确保流在任何情况下都被关闭
                IoUtil.close(os);
                logMemoryUsage("writeImageWithRetry:FinallyAttempt" + retryCount);
            }
        } // end while loop
        logMemoryUsage("writeImageWithRetry:LoopEndReturnFalse");
        return false;
    }

}
