package com.rongchen.byh.common.core.util;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Enumeration;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicLong;
import javax.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 订单号生成工具类
 * 基于雪花算法(Snowflake)实现的分布式唯一订单号生成器
 * 
 * 静态工具类设计，可直接通过类名调用各方法
 *
 * 订单号组成: 业务前缀 + 时间戳 + 机器ID + 序列号
 * - 符号位: 1位,固定为0
 * - 时间戳: 41位,精确到毫秒,可用69年
 * - 机器ID: 10位,最多支持1024个节点
 * - 序列号: 12位,同一毫秒内最多生成4096个序号
 *
 * <AUTHOR>
 * @date 2025-03-12
 */
@Component
public class GeneratorUtil {
    private static final Logger logger = LoggerFactory.getLogger(GeneratorUtil.class);

    // 开始时间戳 (2024-01-01 00:00:00)
    private static final long START_TIMESTAMP = 1704038400000L;

    // 机器ID所占位数
    private static final long WORKER_ID_BITS = 10L;
    // 序列号所占位数
    private static final long SEQUENCE_BITS = 12L;

    // 机器ID最大值 (2^10 - 1 = 1023)
    private static final long MAX_WORKER_ID = ~(-1L << WORKER_ID_BITS);
    // 序列号最大值 (2^12 - 1 = 4095)
    private static final long MAX_SEQUENCE = ~(-1L << SEQUENCE_BITS);

    // 机器ID左移位数 (12)
    private static final long WORKER_ID_SHIFT = SEQUENCE_BITS;
    // 时间戳左移位数 (22)
    private static final long TIMESTAMP_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS;

    // 工作机器ID(0~1023)
    private static volatile long workerId;
    // 毫秒内序列号(0~4095)
    private static final AtomicLong sequence = new AtomicLong(0L);
    // 上次生成ID的时间戳
    private static volatile long lastTimestamp = -1L;

    // 用于自定义订单号长度和格式的模板
    private static final String DEFAULT_FORMAT = "%018d";

    // 用于表示不同业务类型的默认前缀
    public static final String PREFIX_ORDER = "ORD"; // 普通订单
    public static final String PREFIX_PAYMENT = "PAY"; // 支付订单
    public static final String PREFIX_REFUND = "REF"; // 退款订单
    public static final String PREFIX_LOGISTICS = "LOG"; // 物流订单

    // 类是否已初始化标志
    private static volatile boolean initialized = false;
    // 初始化锁
    private static final Object initLock = new Object();

    // Spring注入的WorkerId配置值
    private static long configuredWorkerId = -1;

    // 允许外部手动设置WorkerId
    public static void setWorkerId(long id) {
        if (id > MAX_WORKER_ID || id < 0) {
            throw new IllegalArgumentException(
                    String.format("Worker ID can't be greater than %d or less than 0", MAX_WORKER_ID));
        }
        synchronized (initLock) {
            workerId = id;
            initialized = true;
            logger.info("Worker ID manually set to: {}", workerId);
        }
    }

    // Spring注入配置
    @Value("${snowflake.worker-id:-1}")
    public void setConfiguredWorkerId(long id) {
        configuredWorkerId = id;
    }

    /**
     * 初始化方法，自动分配workerId
     * 支持Spring自动调用和手动调用
     */
    @PostConstruct
    public void init() {
        initializeIfNeeded();
    }

    /**
     * 静态初始化方法，确保在首次使用前进行初始化
     */
    private static void initializeIfNeeded() {
        if (!initialized) {
            synchronized (initLock) {
                if (!initialized) {
                    doInitialize();
                    initialized = true;
                }
            }
        }
    }

    /**
     * 执行实际的初始化逻辑
     */
    private static void doInitialize() {
        // 1. 优先使用配置的workerId
        if (configuredWorkerId >= 0 && configuredWorkerId <= MAX_WORKER_ID) {
            workerId = configuredWorkerId;
            logger.info("Using configured worker ID: {}", workerId);
            return;
        }

        // 2. 尝试从MAC地址获取
        try {
            workerId = generateWorkerIdFromMac();
            logger.info("Using worker ID generated from MAC address: {}", workerId);
            return;
        } catch (Exception e) {
            logger.warn("Failed to generate worker ID from MAC address: {}", e.getMessage());
        }

        // 3. 最后使用随机数
        workerId = ThreadLocalRandom.current().nextLong(MAX_WORKER_ID + 1);
        logger.info("Using randomly generated worker ID: {}", workerId);
    }

    /**
     * 从MAC地址生成workerId
     */
    private static long generateWorkerIdFromMac() throws SocketException {
        try {
            // 尝试获取本机网卡MAC地址
            InetAddress localHost = InetAddress.getLocalHost();
            NetworkInterface network = NetworkInterface.getByInetAddress(localHost);

            if (network == null) {
                // 尝试获取第一个非回环地址的网卡
                Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
                while (networkInterfaces.hasMoreElements()) {
                    NetworkInterface ni = networkInterfaces.nextElement();
                    if (!ni.isLoopback() && ni.getHardwareAddress() != null) {
                        network = ni;
                        break;
                    }
                }
            }

            if (network != null) {
                byte[] mac = network.getHardwareAddress();
                if (mac != null) {
                    // 使用MAC地址的后两个字节生成workerId
                    return ((mac[mac.length - 2] & 0xFF) | ((mac[mac.length - 1] & 0xFF) << 8)) & MAX_WORKER_ID;
                }
            }

            // 如果无法获取MAC地址，使用本地IP地址的哈希值
            byte[] ipAddress = localHost.getAddress();
            return (((ipAddress[ipAddress.length - 2] & 0xFF) << 8) | (ipAddress[ipAddress.length - 1] & 0xFF))
                    & MAX_WORKER_ID;
        } catch (UnknownHostException e) {
            logger.warn("Failed to get hostname (InetAddress.getLocalHost()), using default worker ID 0. Error: {}",
                    e.getMessage());
            return 0L; // Fallback to default worker ID 0
        } catch (SocketException e) {
            logger.warn("Failed to get network interface, using default worker ID 0. Error: {}", e.getMessage());
            return 0L; // Fallback to default worker ID 0
        } catch (Exception e) {
            // Catch other potential exceptions during network interface processing
            logger.error(
                    "Unexpected error while generating worker ID from network interfaces, using default worker ID 0",
                    e);
            return 0L; // Fallback to default worker ID 0
        }
    }

    /**
     * 构造函数，私有化防止实例化
     */
    private GeneratorUtil() {
        // 工具类不应被实例化
    }

    /**
     * 生成订单号
     * 
     * @param prefix 业务前缀
     * @return 订单号
     */
    public static String generateOrderNo(String prefix) {
        initializeIfNeeded();
        long id = nextId();
        return prefix + String.format(DEFAULT_FORMAT, id);
    }

    /**
     * 生成带时间标记的订单号
     * 
     * @param prefix 业务前缀
     * @return 订单号（前缀+年月日+ID）
     */
    public static String generateTimedOrderNo(String prefix) {
        initializeIfNeeded();
        String timeStr = formatDate(System.currentTimeMillis(), "yyyyMMdd");
        return prefix + timeStr + String.format("%010d", nextId() & 0xFFFFFFFFFFL);
    }

    /**
     * 生成订单号（不带前缀）
     * 
     * @return 订单号
     */
    public static String generateOrderNo() {
        initializeIfNeeded();
        return String.format(DEFAULT_FORMAT, nextId());
    }

    /**
     * 生成指定长度的纯数字订单号
     * 
     * @param length 订单号长度
     * @return 订单号
     */
    public static String generateNumericOrderNo(int length) {
        initializeIfNeeded();
        if (length < 6 || length > 20) {
            throw new IllegalArgumentException("Order number length must be between 6 and 20");
        }

        long id = nextId();
        return String.format("%0" + length + "d", id % (long) Math.pow(10, length));
    }

    /**
     * 生成下一个ID (线程安全实现)
     * 
     * @return ID
     */
    public static synchronized long nextId() {
        initializeIfNeeded();
        long timestamp = timeGen();

        // 如果当前时间小于上一次ID生成的时间戳，说明系统时钟回退过，记录并报警
        if (timestamp < lastTimestamp) {
            long offset = lastTimestamp - timestamp;
            if (offset <= 5) {
                // 如果时钟回退在5ms以内，等待双倍时间
                try {
                    Thread.sleep(offset * 2);
                    timestamp = timeGen();
                    if (timestamp < lastTimestamp) {
                        logger.error("Clock is still moving backwards after waiting {} ms", offset * 2);
                        throw new RuntimeException(String.format(
                                "Clock moved backwards. Refusing to generate id for %d milliseconds",
                                lastTimestamp - timestamp));
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new RuntimeException("Interrupted while waiting for clock to catch up", e);
                }
            } else {
                // 如果时钟回退超过5ms，记录错误并抛出异常
                logger.error("Clock moved backwards by {} milliseconds. Refusing to generate ID", offset);
                throw new RuntimeException(String.format(
                        "Clock moved backwards. Refusing to generate id for %d milliseconds",
                        offset));
            }
        }

        // 如果是同一时间生成的，则进行毫秒内序列
        if (lastTimestamp == timestamp) {
            // 使用AtomicLong保证线程安全
            long seq = sequence.incrementAndGet() & MAX_SEQUENCE;
            sequence.set(seq);

            // 毫秒内序列溢出
            if (seq == 0) {
                // 阻塞到下一个毫秒，获得新的时间戳
                timestamp = tilNextMillis(lastTimestamp);
            }
        } else {
            // 时间戳改变，毫秒内序列重置
            sequence.set(0L);
        }

        // 上次生成ID的时间戳
        lastTimestamp = timestamp;

        // 移位并通过或运算拼到一起组成64位的ID
        return ((timestamp - START_TIMESTAMP) << TIMESTAMP_SHIFT)
                | (workerId << WORKER_ID_SHIFT)
                | sequence.get();
    }

    /**
     * 阻塞到下一个毫秒，直到获得新的时间戳
     * 
     * @param lastTimestamp 上次生成ID的时间戳
     * @return 新的时间戳
     */
    private static long tilNextMillis(long lastTimestamp) {
        long timestamp = timeGen();
        int spinCount = 0;
        int maxSpinCount = 100;

        while (timestamp <= lastTimestamp) {
            spinCount++;
            if (spinCount > maxSpinCount) {
                // 自旋次数过多时，适当休眠避免CPU过度占用
                try {
                    Thread.sleep(1);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
            timestamp = timeGen();
        }

        return timestamp;
    }

    /**
     * 返回当前时间戳
     * 
     * @return 当前时间戳
     */
    private static long timeGen() {
        return System.currentTimeMillis();
    }

    /**
     * 格式化日期
     * 
     * @param timestamp 时间戳
     * @param pattern   日期格式
     * @return 格式化后的日期字符串
     */
    private static String formatDate(long timestamp, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(new Date(timestamp));
    }

    /**
     * 提取ID中的时间戳部分
     * 
     * @param id 雪花算法生成的ID
     * @return 时间戳（毫秒）
     */
    public static long extractTimestamp(long id) {
        return (id >> TIMESTAMP_SHIFT) + START_TIMESTAMP;
    }

    /**
     * 提取ID中的工作机器ID部分
     * 
     * @param id 雪花算法生成的ID
     * @return 工作机器ID
     */
    public static long extractWorkerId(long id) {
        return (id >> WORKER_ID_SHIFT) & MAX_WORKER_ID;
    }

    /**
     * 提取ID中的序列号部分
     * 
     * @param id 雪花算法生成的ID
     * @return 序列号
     */
    public static long extractSequence(long id) {
        return id & MAX_SEQUENCE;
    }
}