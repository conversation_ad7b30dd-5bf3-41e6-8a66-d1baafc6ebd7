package com.rongchen.byh.common.core.util; // 包名根据用户指定

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class Md5Utils {

    private static final char[] HEX_DIGITS = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd',
            'e', 'f' };

    /**
     * 计算字符串的32位小写MD5值 (UTF-8编码)
     * 如果输入是身份证号且包含 'x'，则先转为 'X' 再计算。
     * 
     * @param text     输入字符串
     * @param isIdCard 是否为身份证号 (用于处理 'x')
     * @return 32位小写MD5值，如果输入为null或计算失败则返回null
     */
    public static String encrypt(String text, boolean isIdCard) {
        if (text == null) {
            return null;
        }
        try {
            String processedText = text;
            // 身份证号特殊处理：小写x转大写X
            if (isIdCard && processedText.contains("x")) {
                processedText = processedText.replace('x', 'X');
            }

            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(processedText.getBytes(StandardCharsets.UTF_8));

            // 转换为16进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                hexString.append(HEX_DIGITS[(b >> 4) & 0x0F]);
                hexString.append(HEX_DIGITS[b & 0x0F]);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error("MD5加密失败: {}", text, e);
            return null;
        }
    }

    /**
     * 计算普通字符串的MD5
     */
    public static String encryptName(String name) {
        return encrypt(name, false);
    }

    /**
     * 计算身份证号的MD5
     */
    public static String encryptIdCard(String idCard) {
        return encrypt(idCard, true);
    }

    public static void main(String[] args) {
        System.out.println(encryptName("张三"));
        System.out.println(encryptIdCard("123456789012345678"));
    }
}