# 外部渠道API接口文档

> **说明：**
> 本接口文档仅描述接口路径、HTTP方法、功能说明及参数传递方式。所有接口的参数与返回值格式完全依赖各上游平台对接文档，系统仅做基础校验。字段定义、示例及格式请务必参考各平台官方文档。

---

## 接口调用流程图

```mermaid
flowchart TD
    A[外部平台/第三方系统] -->|HTTP POST| B[业务Controller 如CreditApiController]
    B -->|参数原样透传| C[BaseApiController]
    C -->|分发到对应渠道适配器| D[ChannelAdapter平台适配器]
    D -->|调用核心业务逻辑| E[核心业务处理/服务层]
    E -->|处理结果| F[ChannelAdapter]
    F -->|封装平台格式响应| G[BaseApiController]
    G -->|返回响应| H[业务Controller]
    H -->|HTTP响应| I[外部平台/第三方系统]
    D -.->|异常/错误| X[异常处理/错误响应封装]
    X -.-> G
```

> **流程说明：**
> 
> 1. 外部(第三方)平台通过HTTP POST请求调用我方 Controller 接口。
> 2. Controller将请求参数原样透传给BaseApiController。
> 3. BaseApiController根据平台和操作类型分发到对应的ChannelAdapter（适配器）。
> 4. ChannelAdapter负责参数预处理、核心业务逻辑调用、后置处理（如签名、加密等）。
> 5. 核心业务处理完成后，结果经适配器封装为平台要求的响应格式。
> 6. BaseApiController统一返回响应给业务Controller，最终响应至外部平台。
> 7. 若处理过程中发生异常，适配器或BaseApiController会统一封装错误响应返回。
> 8. 所有参数与返回值参考各平台对接文档。

---

## 授信接口（CreditApiController）

| 接口路径 | HTTP方法 | 功能说明 | 参数传递方式 |
|---|---|---|---|
| /webOutApi/{channel}/credit | POST | 进件推送(授信) | 根据平台文档确定， |
| /webOutApi/{channel}/creditQuery | POST | 进件(授信)结果查询 | 根据平台文档确定， |
| /webOutApi/{channel}/userAccess | POST | 用户准入(撞库) | 根据平台文档确定， |
| /webOutApi/{channel}/protocolQuery | POST | 协议查询 | 根据平台文档确定， |
| /webOutApi/{channel}/creditLimitQuery | POST | 授信额度查询 | 根据平台文档确定， |

> **说明：**  
> 所有参数与返回值请参考各平台对接文档，系统仅做基础校验。

---

## 借款接口（LoanApiController）

| 接口路径 | HTTP方法 | 功能说明 | 参数传递方式 |
|---|---|---|---|
| /webOutApi/{channel}/loanTrial | POST | 借款试算 | 根据平台文档确定， |
| /webOutApi/{channel}/loanApply | POST | 借款申请 | 根据平台文档确定， |
| /webOutApi/{channel}/loanQuery | POST | 借款结果查询 | 根据平台文档确定， |

> **说明：**  
> 所有参数与返回值请参考各平台对接文档，系统仅做基础校验。

---

## 还款接口（RepayApiController）

| 接口路径 | HTTP方法 | 功能说明 | 参数传递方式 |
|---|---|---|---|
| /webOutApi/{channel}/repayTrial | POST | 还款试算 | 根据平台文档确定， |
| /webOutApi/{channel}/repayApply | POST | 还款申请 | 根据平台文档确定， |
| /webOutApi/{channel}/repayPlanQuery | POST | 还款计划查询 | 根据平台文档确定， |
| /webOutApi/{channel}/repayResultQuery | POST | 还款结果查询 | 根据平台文档确定， |

> **说明：**  
> 所有参数与返回值请参考各平台对接文档，系统仅做基础校验。

---

## 银行卡接口（CardApiController）

| 接口路径 | HTTP方法 | 功能说明 | 参数传递方式 |
|---|---|---|---|
| /webOutApi/{channel}/cardSupportList | POST | 支持银行卡列表 | 根据平台文档确定， |
| /webOutApi/{channel}/cardBindSms | POST | 绑卡短信发送 | 根据平台文档确定， |
| /webOutApi/{channel}/cardBindValidate | POST | 绑卡提交 | 根据平台文档确定， |
| /webOutApi/{channel}/cardBindQuery | POST | 银行卡绑卡查询 | 根据平台文档确定， |
| /webOutApi/{channel}/cardBindList | POST | 已绑卡列表 | 根据平台文档确定， |

> **说明：**  
> 所有参数与返回值请参考各平台对接文档，系统仅做基础校验。

---

## 订单接口（OrderApiController）

| 接口路径 | HTTP方法 | 功能说明 | 参数传递方式 |
|---|---|---|---|
| /webOutApi/{channel}/orderStatus | POST | 订单状态查询 | 根据平台文档确定， |

> **说明：**  
> 所有参数与返回值请参考各平台对接文档，系统仅做基础校验。

---

## 特殊接口（SpecialApiController）

| 接口路径 | HTTP方法 | 功能说明 | 参数传递方式 |
|---|---|---|---|
| /webOutApi/{channel}/loanH5Url | POST | 借款H5页面地址获取 | 根据平台文档确定， |
| /webOutApi/{channel}/repayH5Url | POST | 还款H5页面地址获取 | 根据平台文档确定， |

> **说明：**  
> 所有参数与返回值请参考各平台对接文档，系统仅做基础校验。

--- 