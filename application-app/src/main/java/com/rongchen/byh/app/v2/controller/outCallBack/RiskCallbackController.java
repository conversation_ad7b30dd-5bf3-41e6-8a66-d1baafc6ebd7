package com.rongchen.byh.app.v2.controller.outCallBack;

import cn.dev33.satoken.annotation.SaIgnore;
import com.alibaba.fastjson.JSONObject;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.rongchen.byh.app.dto.api.RiskCallBackDto;
import com.rongchen.byh.app.v2.handle.channelRisk.ChannelRiskHandleCenter;
import com.rongchen.byh.app.v2.handle.channelRisk.ChannelRiskHandleFactory;
import com.rongchen.byh.app.web.api.adapter.AbstractChannelAdapter;
import com.rongchen.byh.app.web.api.common.ApiOperation;
import com.rongchen.byh.common.core.annotation.DisableDataFilter;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 流量方风控回调接口
 * @date 2025/5/7 16:04
 */
@ApiSupport(order = 1)
@Tag(name = "流量方风控回调接口")
@DisableDataFilter
@Slf4j
@RestController
@RequestMapping("/out/risk/{channel}")
public class RiskCallbackController {
    @PostMapping("/riskCallBack")
    @Operation(summary = "风控回调接口")
    @SaIgnore
    public JSONObject riskCallBack(@PathVariable String channel,@RequestBody RiskCallBackDto riskCallBackDto) {
        ChannelRiskHandleFactory channelRiskHandle = ChannelRiskHandleCenter.getChannelRiskHandle(channel);

        return channelRiskHandle.riskCallBack(riskCallBackDto);
    }

}