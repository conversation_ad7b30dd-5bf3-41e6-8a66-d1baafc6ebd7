package com.rongchen.byh.app.v2.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.v2.dao.UserCapitalCreditRecordMapper;
import com.rongchen.byh.app.v2.entity.UserCapitalCreditRecord;
import com.rongchen.byh.app.v2.service.CommonDisburseService;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 用户授信状态查询定时任务
 * @date 2025/4/28 16:51:04
 */
@Component
@Slf4j
public class UserCapitalCreditRecordJob {
    @Resource
    private UserCapitalCreditRecordMapper userCapitalCreditRecordMapper;
    @Resource
    private CommonDisburseService commonDisburseService;
    @Resource(name = "taskExecutor")
    private Executor taskExecutor;

    @XxlJob("userCapitalCreditRecordJob")
    public void userCapitalCreditRecordJob() {
        // 设置全局TraceId
        try {
            String jobTraceId = MDCUtil.generateTraceId();
            MDCUtil.setTraceId(jobTraceId);
            log.info("=========用户授信状态查询定时任务开始执行======");
            // 查询进行中的数据
            String param = XxlJobHelper.getJobParam();
            Integer day = 1;
            if (StrUtil.isNotEmpty(param)) {
                day = Integer.valueOf(param);
            }
            List<UserCapitalCreditRecord> list = userCapitalCreditRecordMapper.selectInProcessList(day);
            if (CollUtil.isEmpty(list)) {
                log.info("=========没有进行中的数据=========");
                return;
            }
            int batchSize = 10;
            int totalRequests = list.size();

            List<UserCapitalCreditRecord> failList = new ArrayList<>();

            for (int i = 0; i < totalRequests; i += batchSize) {
                int end = Math.min(i + batchSize, totalRequests);
                List<UserCapitalCreditRecord> batch = list.subList(i, end);

                log.info("处理第 {} 批数据，", i);
                // 并行处理当前批次的请求
                List<CompletableFuture<?>> futures = batch.stream()
                        .map(data -> CompletableFuture.supplyAsync(
                                () -> {
                                    String newTranceId = MDCUtil.generateTraceId();
                                    MDCUtil.setTraceId(newTranceId);
                                    return commonDisburseService.creditStatusQuery(data);}, taskExecutor).thenAccept(result -> {
                            if (!result.isSuccess()) {
                                failList.add(data);
                            }
                        }).exceptionally(e -> {
                            log.error("定时查询授信状态失败，订单id：{}", data.getId(), e);
                            failList.add(data);
                            return null;
                        }))
                        .collect(Collectors.toList());

                // 等待所有请求完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(20, TimeUnit.SECONDS);
            }
            log.info("===========执行授信状态主动查询定时任务结束，失败列表：{}======", JSONObject.toJSONString(failList));
        } catch (Exception e) {
            log.error("执行授信状态主动查询定时任务异常", e);
        } finally {
            MDCUtil.clear();
        }
    }
}
