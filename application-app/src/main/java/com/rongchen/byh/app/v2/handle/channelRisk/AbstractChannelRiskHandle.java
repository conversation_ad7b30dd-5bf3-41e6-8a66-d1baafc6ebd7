package com.rongchen.byh.app.v2.handle.channelRisk;

import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.dto.api.RiskCallBackDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 抽象公共类
 * @date 2025/5/7 16:55
 */
@Slf4j
@Component
public abstract class AbstractChannelRiskHandle implements ChannelRiskHandleFactory{
    @Override
    public JSONObject riskCallBack(RiskCallBackDto riskCallBackDto) {
        return null;
    }
}