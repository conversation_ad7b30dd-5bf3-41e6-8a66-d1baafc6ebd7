package com.rongchen.byh.app.v2.controller.app;


import com.rongchen.byh.app.dto.app.FaceVerifyDto;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.app.v2.dto.capital.bindcard.*;
import com.rongchen.byh.app.v2.dto.capital.loan.CommonLoanApplyDto;
import com.rongchen.byh.app.v2.dto.capital.loan.CommonTrialPaymentDto;
import com.rongchen.byh.app.v2.service.CommonDisburseService;
import com.rongchen.byh.app.v2.vo.capital.loan.CommonLoanApplyVo;
import com.rongchen.byh.common.api.zifang.vo.*;
import com.rongchen.byh.common.api.zifang.vo.contraact.ContractList;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 通用支用接口
 * @date 2025/4/25 16:03:04
 */
@Tag(name = "用户支用接口")
@RestController
@RequestMapping("/v2/disburse")
public class CommonDisburseController {
    @Resource
    private CommonDisburseService commonDisburseService;

    @Resource
    private RedissonClient redissonClient;

    @Operation(summary = "借款试算")
    @PostMapping("/trialPayment")
    public ResponseResult<RepayPlanCalcVo> trialPayment(@RequestBody CommonTrialPaymentDto dto){
        return commonDisburseService.trialPayment(dto);
    }


    @Operation(summary = "协议查询")
    @PostMapping("/queryAgreement")
    ResponseResult<List<ContractList>> queryAgreement(@RequestBody CommonAgreementDto commonAgreementDto){
        return commonDisburseService.queryAgreement(commonAgreementDto);
    }

    @Operation(summary = "绑卡-支付通道")
    @PostMapping("/loanElement")
    public ResponseResult<LoanElementVo> bindBackPay(@RequestBody CommonLoanElementDto dto){
        return commonDisburseService.loanElement(dto);
    }


    @Operation(summary = "绑卡-获取验证码")
    @PostMapping("/bindSendCode")
    public ResponseResult<BindBankSmsVo> bindSendCode(@RequestBody CommonBindCardSmsDto dto){
        return commonDisburseService.bindSendCode(dto);
    }


    @Operation(summary = "绑卡-提交验证码")
    @PostMapping("/verifyBindSend")
    public ResponseResult<VerifyBindBankSmsVo> verifyBindSend(@RequestBody CommonVerifyBindBankSmsDto dto){
        return commonDisburseService.verifyBindSend(dto);
    }


    @Operation(summary = "绑卡-结果查询 ")
    @PostMapping("/bindBankResult")
    public ResponseResult<QueryBindBankResultVo> bindBankResult(@RequestBody CommonQueryBindBankResultDto dto){
        return commonDisburseService.bindBankResult(dto);
    }

    @Operation(summary = "二次过人脸")
    @PostMapping("/submitFace")
    public ResponseResult<Void> submitFace(@RequestBody FaceVerifyDto verifyDto){
        return commonDisburseService.submitFace(verifyDto);
    }

    @Operation(summary = "借款申请")
    @PostMapping("/loanApply")
    public ResponseResult<CommonLoanApplyVo> loanApply(@RequestBody CommonLoanApplyDto dto){
        Long userId = UserTokenUtil.getUserId();
        RLock lock = redissonClient.getLock("loanApply" + userId);
        boolean isLocked = false;
        try {
            isLocked = lock.tryLock(5, TimeUnit.SECONDS);
            if (!isLocked) {
                return ResponseResult.error(ErrorCodeEnum.FAIL, "系统繁忙，请稍后再试！");
            }
            return commonDisburseService.loanApply(dto);
        } catch (Exception e) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "系统异常");
        } finally {
            if (isLocked) {
                lock.unlock();
            }
        }

    }
}
