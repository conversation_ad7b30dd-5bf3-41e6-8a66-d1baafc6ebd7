package com.rongchen.byh.app.v2.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.rongchen.byh.app.dao.UserDataMapper;
import com.rongchen.byh.app.dao.UserDetailMapper;
import com.rongchen.byh.app.dao.UserLoveLogMapper;
import com.rongchen.byh.app.entity.UserData;
import com.rongchen.byh.app.entity.UserDetail;
import com.rongchen.byh.app.entity.UserLoveLog;
import com.rongchen.byh.app.loveSign.LoveSignProperties;
import com.rongchen.byh.app.loveSign.LoveSignService;
import com.rongchen.byh.app.loveSign.SignDto;
import com.rongchen.byh.app.loveSign.SignRulesDto;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.app.v2.common.FinalParam;
import com.rongchen.byh.app.v2.entity.AgreementData;
import com.rongchen.byh.app.v2.service.AiSignService;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 爱签业务实现类
 * @date 2025/4/27 14:19:12
 */
@Service
@Slf4j
public class AiSignServiceImpl implements AiSignService {
    @Resource
    private UserDetailMapper userDetailMapper;
    @Resource
    private UserDataMapper userDataMapper;
    @Resource
    private LoveSignService loveSignService;
    @Resource
    private LoveSignProperties loveSignProperties;
    @Resource
    private UserLoveLogMapper userLoveLogMapper;
    @Resource(name = "taskExecutor")
    private Executor taskExecutor;

    @Override
    public ResponseResult<Void> centralBankCreditSign(Long userId, List<AgreementData> agreements, List<String> contactNoList) {
        UserData userData = userDataMapper.selectById(userId);
        UserDetail detail = userDetailMapper.queryByUserId(userId);
        SignDto signDto = new SignDto();
        signDto.setName(detail.getWebName());
        signDto.setAccount(userData.getMobile());
        signDto.setIdCard(detail.getWebIdCard());
        signDto.setUserId(userId);
        List<SignRulesDto> rules = new ArrayList<>();

        List<CompletableFuture<?>> futures = new ArrayList<>();
        List<AgreementData> failList = new ArrayList<>();
        for (AgreementData one : agreements) {
            futures.add(CompletableFuture.supplyAsync(() -> {
                ResponseResult<Void> result = ResponseResult.success();
                Integer id = one.getId();
                if (id.equals(FinalParam.FACE_AGREEAMENT)) {
                    signDto.setContractNo(IdUtil.fastSimpleUUID());
                    signDto.setContractName("人脸识别授权书");
                    List<SignRulesDto> rules1 = createRules(userData, detail, id);
                    signDto.setRules(rules1);
                    result = loveSignService.signAll(signDto, loveSignProperties.getFace());
                    loveSign(signDto, result);
                    if (!result.isSuccess()) {
                        return ResponseResult.error(ErrorCodeEnum.FAIL, "人脸识别授权书签名失败");
                    }
                    contactNoList.add(signDto.getContractNo());
                } else if (id.equals(FinalParam.INFO_SHARE_AGREEAMENT)) {
                    signDto.setContractNo(IdUtil.fastSimpleUUID());
                    signDto.setRules(rules);
                    signDto.setContractName("个人信息共享授权书");
                    result = loveSignService.signAll(signDto, loveSignProperties.getPerson());
                    loveSign(signDto, result);
                    if (!result.isSuccess()) {
                        return ResponseResult.error(ErrorCodeEnum.FAIL, "个人信息共享授权书签名失败");
                    }
                    contactNoList.add(signDto.getContractNo());
                } else if (id.equals(FinalParam.ELECTRONIC_AGREEAMENT)) {
                    signDto.setContractNo(IdUtil.fastSimpleUUID());
                    signDto.setContractName("电子签授权书");
                    result = loveSignService.signAll(signDto, loveSignProperties.getSign());
                    loveSign(signDto, result);
                    if (!result.isSuccess()) {
                        return ResponseResult.error(ErrorCodeEnum.FAIL, "电子签授权书签名失败");
                    }
                    contactNoList.add(signDto.getContractNo());
                } else if (id.equals(FinalParam.Credit_AGREEAMENT)) {
                    signDto.setContractNo(IdUtil.fastSimpleUUID());
                    signDto.setContractName("征信查询授权书");
                    List<SignRulesDto> rules1 = createRules(userData, detail, id);
                    signDto.setRules(rules1);
                    result = loveSignService.signAll(signDto, loveSignProperties.getCredit());
                    loveSign(signDto, result);
                    if (!result.isSuccess()) {
                        return ResponseResult.error(ErrorCodeEnum.FAIL, "征信查询授权书签名失败");
                    }
                    contactNoList.add(signDto.getContractNo());
                } else if (id.equals(FinalParam.ENTRUST_AGREEAMENT)) {
                    signDto.setContractNo(IdUtil.fastSimpleUUID());
                    signDto.setContractName("委托担保申请书");
                    List<SignRulesDto> rules1 = createRules(userData, detail, id);
                    signDto.setRules(rules1);
                    result = loveSignService.signAll(signDto, loveSignProperties.getEntrusted());
                    loveSign(signDto, result);
                    if (!result.isSuccess()) {
                        return ResponseResult.error(ErrorCodeEnum.FAIL, "委托担保申请书签名失败");
                    }
                    contactNoList.add(signDto.getContractNo());
                }
                return result;
            }, taskExecutor).thenApply(rs -> {
                if (!rs.isSuccess()) {
                    failList.add(one);
                }
                return null;
            }).exceptionally(e -> {
                log.error("异步签署任务执行异常", e);
                failList.add(one);
                return null;
            }));
        }
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("异步签署任务执行异常", e);
            return ResponseResult.error(ErrorCodeEnum.FAIL, "异步签署爱签任务执行异常");
        }
        if (ObjectUtil.isEmpty(failList)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "异步签署爱签部分失败");
        }
        return ResponseResult.success();
    }

    public void loveSign(SignDto dto, ResponseResult<Void> result) {
        UserLoveLog log = new UserLoveLog();
        log.setUserId(UserTokenUtil.getUserId());
        log.setContractName(dto.getContractName());
        log.setContractNo(dto.getContractNo());
        if (result.isSuccess()) {
            log.setContractStatus(1);
        } else {
            log.setContractStatus(2);
        }
        userLoveLogMapper.insert(log);
    }

    public List<SignRulesDto> createRules(UserData userData, UserDetail detail, Integer id) {
        List<SignRulesDto> rules = new ArrayList<>();
        if (id.equals(FinalParam.FACE_AGREEAMENT)) {
            SignRulesDto rulesDto = new SignRulesDto();
            rulesDto.setKey("platformName");
            rulesDto.setValue("七叶草");
            rules.add(rulesDto);
            return rules;
        } else if (id.equals(FinalParam.Credit_AGREEAMENT) || id.equals(FinalParam.ENTRUST_AGREEAMENT)) {
            SignRulesDto signRulesDto1 = new SignRulesDto();
            SignRulesDto signRulesDto2 = new SignRulesDto();
            SignRulesDto signRulesDto3 = new SignRulesDto();
            SignRulesDto signRulesDto4 = new SignRulesDto();
            SignRulesDto signRulesDto5 = new SignRulesDto();
            SignRulesDto signRulesDto6 = new SignRulesDto();
            signRulesDto1.setKey("idNumber");
            signRulesDto1.setValue(detail.getWebIdCard());
            signRulesDto2.setKey("signUserName");
            signRulesDto2.setValue(detail.getWebName());
            signRulesDto3.setKey("mobile");
            signRulesDto3.setValue(userData.getMobile());
            signRulesDto4.setKey("address");
            signRulesDto4.setValue(detail.getAddress());
            signRulesDto5.setKey("signDate");
            signRulesDto5.setValue(new SimpleDateFormat("yyyy 年 MM 月 dd 日").format(new Date()));
            signRulesDto6.setKey("type");
            signRulesDto6.setValue("身份证");
            rules.add(signRulesDto1);
            rules.add(signRulesDto2);
            rules.add(signRulesDto3);
            rules.add(signRulesDto4);
            rules.add(signRulesDto5);
            rules.add(signRulesDto6);
            return rules;
        } else {
            return rules;
        }

    }
}
