<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.v2.dao.UserProgressMapper">
    <resultMap id="UserProgressResult" type="com.rongchen.byh.app.v2.entity.UserProgress">
        <id property="id" column="id"></id>
        <result property="userId" column="user_id"></result>
        <result property="pageId" column="page_id"></result>
        <result property="createTime" column="create_time"></result>
        <association property="pageMenu" javaType="com.rongchen.byh.app.v2.entity.ProductPageMenu">
            <id property="id" column="pid"></id>
            <result property="pageName" column="page_name"></result>
            <result property="curPage" column="cur_page"></result>

            <result property="sort" column="sort"></result>
            <result property="productId" column="product_id"></result>
            <result property="processStatus" column="process_status"></result>
            <result property="hasAgreement" column="has_agreement"></result>

            <collection property="agreementDataList" javaType="java.util.List" ofType="com.rongchen.byh.app.v2.entity.AgreementData">
                <result property="id" column="adId"></result>
                <result property="name" column="name"></result>
                <result property="content" column="content"></result>
            </collection>
        </association>
    </resultMap>
    <!-- 单条 SQL 实现存在更新，不存在新增 -->
    <insert id="updateOrInsertByUserId" parameterType="com.rongchen.byh.app.v2.entity.UserProgress">
        INSERT INTO user_progress (user_id,
                                   page_id,
                                   create_time)
        VALUES (#{userId},
                #{pageId},
                #{createTime}) ON DUPLICATE KEY
        UPDATE
            user_id = VALUES (user_id),
            page_id = VALUES (page_id),
            create_time = VALUES (create_time)
    </insert>
    <delete id="delUserPrograss">
        delete from user_progress where user_id = #{userId}
    </delete>

    <select id="selectByUserId" resultMap="UserProgressResult">
        select
        user_progress.id,
        user_progress.user_id,
        user_progress.page_id,
        user_progress.create_time,
        product_page_menu.id pid,
        product_page_menu.cur_page,
        product_page_menu.has_agreement,
        product_page_menu.sort,
        product_page_menu.product_id,
        product_page_menu.is_error,
        product_page_menu.process_status,
        ad.name,
        ad.content,
        ad.id adId
        from user_progress
        left join product_page_menu on user_progress.page_id = product_page_menu.id
        LEFT JOIN agreement_page_relation apr ON product_page_menu.id = apr.page_id
        LEFT JOIN agreement_data ad ON ad.id = apr.agreement_id
        <where>
            user_id = #{userId}
        </where>
    </select>
</mapper>
