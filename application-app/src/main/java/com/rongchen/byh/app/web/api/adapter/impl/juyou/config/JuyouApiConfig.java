package com.rongchen.byh.app.web.api.adapter.impl.juyou.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 聚有渠道 API 配置
 * <p>
 * 从 application.yml 加载 juyou 前缀下的配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "juyou")
public class JuyouApiConfig {

    /**
     * 聚有提供的 RSA 公钥 (Base64, X.509)
     * 用于：
     * 1. 验签聚有发送过来的请求的 sign 字段。
     * 2. 加密我方响应中返回给聚有的 AES 密钥 key 字段。
     */
    private String publicKey;

    /**
     * 我方提供的 RSA 私钥 (Base64, PKCS#8)
     * 用于：
     * 1. 解密聚有发送过来的请求中的 AES 密钥 key 字段。
     * 2. 签名我方响应中返回给聚有的 sign 字段。
     * 注意: 此密钥可能与其他渠道使用的我方私钥相同或不同，取决于密钥管理策略。
     */
    private String privateKey;

  

}