package com.rongchen.byh.app.v2.handle.product.strategy;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.constant.LoanType;
import com.rongchen.byh.app.constant.SourceMode;
import com.rongchen.byh.app.dto.app.FaceVerifyDto;
import com.rongchen.byh.app.dto.app.IdCardVerifyDto;
import com.rongchen.byh.app.dto.app.LoanApplyDto;
import com.rongchen.byh.app.dto.app.UserFormDto;
import com.rongchen.byh.app.dto.h5.AirRiskControllerDto;
import com.rongchen.byh.app.entity.UserData;
import com.rongchen.byh.app.entity.UserDetail;
import com.rongchen.byh.app.entity.UserLoanApply;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.app.v2.common.RedisEnum;
import com.rongchen.byh.app.v2.dto.LoginH5Dto;
import com.rongchen.byh.app.v2.entity.ProductPageMenu;
import com.rongchen.byh.app.v2.entity.UserProgress;
import com.rongchen.byh.app.v2.handle.product.AbstractProductHandle;
import com.rongchen.byh.app.vo.RegOrLoginVo;
import com.rongchen.byh.app.vo.app.SubmitUserFormVo;
import com.rongchen.byh.app.vo.app.UserFormVo;
import com.rongchen.byh.app.vo.h5.UserCheckLoanApplyVo;
import com.rongchen.byh.common.api.idCardVerify.vo.OcrVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;

/**
 * 空中模式h5仅注册业务处理
 */
@Slf4j
@Component("flyOnlyRegister")
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class FlyOnlyRegisterHandle extends AbstractProductHandle {

	@Override
	public ResponseResult<UserCheckLoanApplyVo> queryLoanResult(Object parame) {
		return super.queryLoanResult(parame);
	}



//	@Override
//	public ResponseResult<Void> submitFaceData(FaceVerifyDto verifyDto) {
//		// todo 人脸识别、电子签、初筛申请、初审风控、推送CRM(区分产品)
//		Long userId = UserTokenUtil.getUserId();
//		UserDetail userDetail = userDetailMapper.queryByUserId(userId);
//
//		// 初审风控
//
//		// 记录申请
//		UserLoanApply apply = new UserLoanApply();
//		apply.setUserId(userId);
//		apply.setApplyType(LoanType.CHECK);
//		apply.setOnlineType(SourceMode.AIR_ONLY_REGISTER);
//		userLoanApplyMapper.insert(apply);
//
//		AirRiskControllerDto airRiskControllerDto = new AirRiskControllerDto();
//		airRiskControllerDto.setUserName(userDetail.getAppName());
//		airRiskControllerDto.setIdNumber(userDetail.getIdNumber());
//		airRiskControllerDto.setUserId(userId);
//		airRiskControllerDto.setType(SourceMode.AIR_ONLY_REGISTER);
//		airRiskControllerDto.setApplyId(apply.getId());
//		rabbitTemplate.convertAndSend(QueueConstant.RISK_CONTROL_QUEUE, JSONObject.toJSONString(airRiskControllerDto));
//		return ResponseResult.success(null);
//	}

//	@Override
//	public ResponseResult<Void> submitOcrData(IdCardVerifyDto verifyDto) {
//		// todo 电子签、三要素、身份证校验
//
//		// 电子签
//
//		// ocr提取身份证校验
//		super.ocrDataVerify(verifyDto);
//
//		//三要素校验
//		super.verifyThree();
//
//
//		return super.submitOcrData(verifyDto);
//	}
//
//	@Override
//	public ResponseResult<UserCheckLoanApplyVo> apply(UserCheckLoanApplyDto userCheckLoanApplyDto, Object params) {
//		// todo 发送mq，通知风控
//		Long userId = UserTokenUtil.getUserId();
//		UserData userData = userDataMapper.selectById(userId);
//		if (ObjectUtil.isEmpty(userData)) {
//			log.error("用户不存在");
//			return ResponseResult.error(ErrorCodeEnum.FAIL, "用户不存在");
//		}
//		UserDetail userDetail = userDetailMapper.queryByUserId(userId);
//		if (ObjectUtil.isEmpty(userDetail)) {
//			log.error("用户信息不存在");
//			return ResponseResult.error(ErrorCodeEnum.FAIL, "用户信息不存在");
//		}
//		// 前置校验
//		if (!IdcardUtil.isValidCard(userDetail.getIdNumber())) {
//			log.error("身份证号格式错误");
//			return ResponseResult.error(ErrorCodeEnum.FAIL, "身份证号格式错误");
//		}
//		// 是否已经提交过一次申请，是则提示已经提交无需再次提交
//		UserData userDataUp = new UserData();
//		userDataUp.setId(userData.getId());
//
//		UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, LoanType.CHECK, SourceMode.AIR_ONLY_REGISTER);
//		UserCheckLoanApplyVo userCheckLoanApplyVo = new UserCheckLoanApplyVo();
//		if (userLoanApply != null) {
//			UserCheckLoanApplyVo result = userCheckLoanApplyVo;
//			result.setUserId(userId);
//			result.setAuditStatus(userLoanApply.getAuditsStatus());
//			log.error("{}已经提交过一次申请，无需再次提交", userId);
//			return ResponseResult.error(ErrorCodeEnum.FAIL, "已经提交过一次申请，无需再次提交");
//		}
//		//三要素校验
//		VerifyVo verifyResult = idCardVerifyService.verifyThree(userDetail.getIdNumber(),
//				userDetail.getAppName(),userData.getMobile());
//		if (verifyResult.getCode() != 1) {
//			log.info("三要素校验失败:{}", userId);
//			return ResponseResult.error(ErrorCodeEnum.FAIL, "三要素校验失败");
//		}
//		// 记录申请
//		UserLoanApply apply = new UserLoanApply();
//		apply.setUserId(userId);
//		apply.setApplyType(LoanType.CHECK);
//		apply.setOnlineType(SourceMode.AIR_ONLY_REGISTER);
//		userLoanApplyMapper.insert(apply);
//
//		AirRiskControllerDto airRiskControllerDto = new AirRiskControllerDto();
//		airRiskControllerDto.setUserName(userDetail.getAppName());
//		airRiskControllerDto.setIdNumber(userDetail.getIdNumber());
//		airRiskControllerDto.setUserId(userId);
//		airRiskControllerDto.setType(SourceMode.AIR_ONLY_REGISTER);
//		airRiskControllerDto.setApplyId(apply.getId());
//		rabbitTemplate.convertAndSend(QueueConstant.RISK_CONTROL_QUEUE, JSONObject.toJSONString(airRiskControllerDto));
//		return ResponseResult.success(null);
//	}

		@Override
		public ResponseResult<SubmitUserFormVo> submitUserForm(UserFormDto dto) {
			// todo 电子签？
			return super.submitUserForm(dto);
		}

		@Override
		public void loanApply(LoanApplyDto loanApplyDto, Object params) {

		}

		@Override
		public ResponseResult<RegOrLoginVo> loginByH5(LoginH5Dto loginH5Dto, UserData userData) {
			// 绑定销售
			ResponseResult<RegOrLoginVo> result = longinBindStaff(userData.getId(), loginH5Dto.getInviteCode());
			if (!result.isSuccess()) {
				return result;
			}

			UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userData.getId(), LoanType.CHECK, userData.getSourceMode());
			UserDetail userDetail = userDetailMapper.queryByUserId(userData.getId());
			RegOrLoginVo regOrLoginVo = new RegOrLoginVo();
			if (ObjectUtil.isNotEmpty(userDetail) && ObjectUtil.isNotEmpty(userDetail.getIdNumber())) {
				regOrLoginVo.setApplyStatus(1);
			}
			if (ObjectUtil.isNotEmpty(userLoanApply)) {
				regOrLoginVo.setApplyStatus(2);
			}
			return ResponseResult.success(regOrLoginVo);
		}

		@Override
		public ResponseResult<OcrVo> submitOcrData(IdCardVerifyDto verifyDto, HttpServletRequest request) {
			ResponseResult<OcrVo> ocrVoResponseResult = super.submitOcrData(verifyDto, request);
			Long userId = UserTokenUtil.getUserId();
			UserData userData = userDataMapper.selectById(userId);
			if (!ocrVoResponseResult.isSuccess()){
				return ocrVoResponseResult;
			}
			// 三要素校验
			ResponseResult<Void> verifyThreeResult = super.verifyThree(ocrVoResponseResult.getData().getIdCardResult().getIdNum(),ocrVoResponseResult.getData().getIdCardResult().getName(),userData.getMobile());
			if (!verifyThreeResult.isSuccess()){
				return ResponseResult.error(ErrorCodeEnum.FAIL, verifyThreeResult.getErrorMessage());
			}
			// 电子签,后置？
			return ocrVoResponseResult;
		}

		@Override
		public ResponseResult<Void> submitFaceData(FaceVerifyDto verifyDto) {
		ResponseResult<Void> voidResponseResult = super.submitFaceData(verifyDto);
		if (!voidResponseResult.isSuccess()) {
			return voidResponseResult;
		}
		//1.进行初筛申请
		//1.1校验参数
		Long userId = UserTokenUtil.getUserId();
		UserData userData = userDataMapper.selectById(userId);
		UserDetail userDetail = userDetailMapper.queryByUserId(userId);
		ResponseResult<Void> flyApplyVerifyResult = super.flyApplyVerify(userData,userDetail);
		if(!flyApplyVerifyResult.isSuccess()){
			return ResponseResult.error(ErrorCodeEnum.FAIL, flyApplyVerifyResult.getErrorMessage());
		}
		//1.2三要素校验
//		ResponseResult<Void> verifyThreeResult = super.verifyThree(userDetail.getWebIdCard(), userDetail.getAppName(),userData.getMobile());
//		if (!verifyThreeResult.isSuccess()){
//			return ResponseResult.error(ErrorCodeEnum.FAIL, verifyThreeResult.getErrorMessage());
//		}
		//1.3记录申请
		// 记录申请
		UserLoanApply apply = new UserLoanApply();
		apply.setUserId(userId);
		apply.setApplyType(LoanType.CHECK);
		apply.setOnlineType(SourceMode.AIR_ONLY_REGISTER);
		userLoanApplyMapper.insert(apply);
		AirRiskControllerDto airRiskControllerDto = new AirRiskControllerDto();
		airRiskControllerDto.setUserName(userDetail.getAppName());
		airRiskControllerDto.setIdNumber(userDetail.getIdNumber());
		airRiskControllerDto.setUserId(userId);
		airRiskControllerDto.setType(SourceMode.AIR_ONLY_REGISTER);
		airRiskControllerDto.setApplyId(apply.getId());
			//		rabbitTemplate.convertAndSend(QueueConstant.RISK_CONTROL_QUEUE, JSONObject.toJSONString(airRiskControllerDto));
		rabbitTemplate.convertAndSend(QueueConstant.RISK_CONTROL_QUEUE_NEW, JSONObject.toJSONString(airRiskControllerDto));
		return voidResponseResult;
	}
	@Override
	public ResponseResult<ProductPageMenu> getPage(Long userId, String curPage, Integer productId) {
		ProductPageMenu beforeMenu = menuMapper.selectByCurPage(curPage,productId);
		Integer beforeSort = beforeMenu.getSort();
		ProductPageMenu returnMenu = new ProductPageMenu();
		//1.先查询redis，看是否有用户的页面位置，如果有获取缓存里面的url,如果没有就查询数据库
		if(redisTemplate.hasKey(RedisEnum.PAGE_USER_ID+userId)){
			ProductPageMenu redisMenu = (ProductPageMenu)redisTemplate.opsForValue().get(RedisEnum.PAGE_USER_ID + userId);
			if(redisMenu.getSort()>beforeSort){
				//说明用户之前执行过相关流程，可以跳步骤,这种情况不需要更新缓存
				//如果pageMenu的curPage值为h5登录页（/login）且是已经有token的情况下,直接跳转到home页面
				if(curPage.equals("/login")){
						beforeMenu.setCurPage("/home");
						beforeMenu.setSort(null);
						returnMenu = beforeMenu;
				}else if(curPage.equals("pages/login/login")){
					//查看是否是app登录页，如果是则直接返回首页
					beforeMenu.setCurPage("pages/home/<USER>");
					beforeMenu.setSort(null);
					returnMenu = beforeMenu;
				}else{
					//直接返回redis存储的页面，回到用户上一次推出的页面，即用户还没有跑完页面程序
					returnMenu = redisMenu;
				}
			} else{
				//说明按照顺序在执行，这种情况需要更新缓存
				beforeMenu.setSort(beforeSort+1);
				beforeMenu.setCurPage(null);
				beforeMenu.setIsError(0);
				ProductPageMenu nextMenu = menuMapper.selectNext(beforeMenu);
				returnMenu= nextMenu;
				//异步存储数据到redis和mysql中
				asyncMethod.updateUserProgress(returnMenu,userId);
			}
		}else{
			//1.缓存过期或者没有缓存，从数据库里面查询用户上一次走到哪一个页面了
			UserProgress progress = userProgressMapper.selectByUserId(userId);
			if(progress!=null){
				ProductPageMenu mysqlMenu = progress.getPageMenu();
				if(mysqlMenu.getSort()>beforeSort){
					//说明用户之前执行过相关流程，可以跳步骤,这种情况不需要更新缓存
					//如果pageMenu的curPage值为h5登录页（/login）且是已经有token的情况下,则直接跳转到home页面
					if(curPage.equals("/login")){
							beforeMenu.setCurPage("/home");
							beforeMenu.setSort(null);
							returnMenu = beforeMenu;
						}
					else if(curPage.equals("pages/login/login")){
						//查看是否是app登录页，如果是则直接返回首页
						beforeMenu.setCurPage("pages/home/<USER>");
						beforeMenu.setSort(null);
						returnMenu = beforeMenu;
					}else{
						//直接返回mysql存储的页面，回到用户上一次推出的页面，即用户还没有跑完页面程序
						returnMenu = mysqlMenu;
					}
				}else{
					//说明按照顺序在执行，这种情况需要更新缓存
					beforeMenu.setSort(beforeSort+1);
					beforeMenu.setIsError(0);
					beforeMenu.setCurPage(null);
					ProductPageMenu menuTwo = menuMapper.selectNext(beforeMenu);
					returnMenu = menuTwo;
					//异步存储数据到redis和mysql中
					asyncMethod.updateUserProgress(returnMenu,userId);
				}
				}else{
					//说明没有跳步骤，直接返回下一个页面，需要更新缓存
					beforeMenu.setSort(beforeSort+1);
					beforeMenu.setIsError(0);
					beforeMenu.setCurPage(null);
					ProductPageMenu menuTwo = menuMapper.selectNext(beforeMenu);
					returnMenu = menuTwo;
					//异步存储数据到redis和mysql中
					asyncMethod.updateUserProgress(returnMenu,userId);
				}
		}
		return ResponseResult.success(returnMenu);
	}
}
