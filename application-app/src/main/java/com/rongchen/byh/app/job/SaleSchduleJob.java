package com.rongchen.byh.app.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.rongchen.byh.app.config.ZifangFactory;
import com.rongchen.byh.app.constant.ZiFangBeanConstant;
import com.rongchen.byh.app.dao.CapitalDataMapper;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.dao.SaleScheduleMapper;
import com.rongchen.byh.app.entity.CapitalData;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.SaleOrderData;
import com.rongchen.byh.app.entity.SaleSchedule;
import com.rongchen.byh.app.service.SaleOrderDataService;
import com.rongchen.byh.app.service.SaleScheduleService;
import com.rongchen.byh.app.utils.NumberUtil;
import com.rongchen.byh.common.api.zifang.dto.SaleApplyDto;
import com.rongchen.byh.common.api.zifang.service.OtherApi;
import com.rongchen.byh.common.api.zifang.vo.RepaymentPlanQueryPkgVo;
import com.rongchen.byh.common.api.zifang.vo.loan.RepayPlanListVo;
import com.rongchen.byh.common.api.zifang.vo.loan.RespSaleRepayPlanVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.hutool.json.XMLTokener.entity;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 同步api赊销还款计划
 * @date 2025/5/16 13:58
 */
@Component
@Slf4j
public class SaleSchduleJob {
    @Resource
    private SaleScheduleService saleScheduleService;
    @Resource
    private DisburseDataMapper disburseDataMapper;
    @Resource(name = "taskExecutor")
    private Executor taskExecutor;

    @Resource
    ZifangFactory zifangFactory;

    @Resource
    CapitalDataMapper capitalDataMapper;

    @Resource
    private SaleScheduleMapper saleScheduleMapper;
    @Resource
    SaleOrderDataService saleOrderDataService;

    @XxlJob("saleSchduleJob")
    public void saleSchduleJob() {
        try {
            String jobTraceId = MDCUtil.generateTraceId();
            MDCUtil.setTraceId(jobTraceId);
            log.info("同步赊销还款计划定时任务开始执行");
            // 需要更新的disburse_id，使用逗号拼接
            // 格式 1,2,3
            String param = XxlJobHelper.getJobParam();
            List<Long> list = null;
            // disburse_id 为空，查询所有订单
            if (StrUtil.isEmpty(param)) {
                list = saleScheduleMapper.selectDisburseIdList(0);
            } else {
                List<String> split = StrUtil.split(param, ",");
                list = split.stream().map(Long::parseLong).collect(Collectors.toList());
            }
            if (CollectionUtil.isEmpty(list)) {
                log.info("【{}】未提供或查询到需要同步的 disburse_id 列表数据为空", jobTraceId);
                return;
            }
            LambdaQueryWrapper<DisburseData> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(DisburseData::getId, list);
            List<DisburseData> disburseDataList = disburseDataMapper.selectList(queryWrapper);
            int batchSize = 10;
            int totalRequests = disburseDataList.size();

            // 查找对应策略
            for (int i = 0; i < totalRequests; i += batchSize) {
                int end = Math.min(i + batchSize, totalRequests);
                List<DisburseData> batch = disburseDataList.subList(i, end);
                log.info("处理第 {} 批数据，", i);
                // 并行处理当前批次的请求
                List<CompletableFuture<?>> futures = batch.stream()
                        .map(data -> CompletableFuture.supplyAsync(
                                () -> {
                                    String newTranceId = MDCUtil.generateTraceId();
                                    MDCUtil.setTraceId(newTranceId);
                                    //查询api的赊销还款计划,并处理
                                    return queryApiSaleSchedule(data);
                                }, taskExecutor).thenAccept(result -> {
                            if (!result.isSuccess()) {

                            }
                        }).exceptionally(e -> {
                            log.error("更新用户赊销订单出错，订单id：{}", data.getId(), e);
                            return null;
                        }))
                        .collect(Collectors.toList());

                // 等待所有请求完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(100, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.error("更新用户赊销订单定时任务执行异常", e);
        } finally {
            MDCUtil.clear();
        }
    }

    public ResponseResult<Void> queryApiSaleSchedule(DisburseData disburseData) {
        //查询出用户的赊销编号
        if (ObjectUtil.isEmpty(disburseData)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_NOT_EXIST);
        }
        String saleNo = disburseData.getSaleNo();
        CapitalData capitalData = capitalDataMapper.selectById(disburseData.getCapitalId());
        if (ObjectUtil.isEmpty(capitalData)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "资方未匹配");
        }

        if (capitalData.getBeanName().equalsIgnoreCase("fenzhua")) {
            return ResponseResult.error(ErrorCodeEnum.FAIL, "新资方无api方式用信");
        }
        OtherApi otherApi = zifangFactory.getApi(capitalData.getBeanName(), OtherApi.class);
        // 分两种情况
        // 1.只有单个赊销订单的情况
        // 2.有多个赊销订单，在单独的赊销订单表中
        if (StrUtil.isNotEmpty(disburseData.getSaleNo())) {
            SaleApplyDto saleApplyDto = new SaleApplyDto();
            saleApplyDto.setLoanNo(disburseData.getLoanNo());
            saleApplyDto.setSaleNo(disburseData.getSaleNo());
            ResponseResult<RespSaleRepayPlanVo> saleApply = otherApi.getApiSaleApply(saleApplyDto);
            if (!saleApply.isSuccess()) {
                log.info("查询API赊销账单 赊销单号：{} 资方返回结果异常：{}", saleNo, saleApply.getErrorMessage());
                return ResponseResult.error(ErrorCodeEnum.FAIL);
            }

            RespSaleRepayPlanVo data = saleApply.getData();
            if (!"0000".equals(data.getResponseCode())) {
                log.info("查询API赊销账单 赊销单号：{} 资方返回结果错误：{}", saleNo, data.getResponseMsg());
                return ResponseResult.error(ErrorCodeEnum.FAIL);
            }

            //对方查询出来的赊销还款计划
            Map<String, RepayPlanListVo> collect = formatPlan(data.getRepayPlanList());;
            //获取我方的赊销还款计划
            List<SaleSchedule> scheduleList = saleScheduleService.list(new LambdaQueryWrapper<SaleSchedule>().eq(SaleSchedule::getDisburseId, disburseData.getId()));
            BigDecimal saleRepayAmount = updateSale(scheduleList, collect);
            // 修改赊销金额
            DisburseData up = new DisburseData();
            up.setId(disburseData.getId());
            up.setSaleRepayAmount(saleRepayAmount);
            disburseDataMapper.updateById(up);
            return ResponseResult.success();
        }

        List<SaleOrderData> dataList = saleOrderDataService.selectListByDisburseId(disburseData.getId());

        List<BigDecimal> totalAmount = new ArrayList<>();
        for (SaleOrderData orderData : dataList) {
            CompletableFuture.supplyAsync(() -> {
                SaleApplyDto saleApplyDto = new SaleApplyDto();
                saleApplyDto.setLoanNo(disburseData.getLoanNo());
                saleApplyDto.setSaleNo(orderData.getSaleNo());
                return otherApi.getApiSaleApply(saleApplyDto);

            }).thenAccept(saleApply -> {
                if (!saleApply.isSuccess()) {
                    log.warn("查询API赊销账单 赊销单号：{} 资方返回结果异常：{}", orderData.getSaleNo(), saleApply.getErrorMessage());
                    return;
                }
                RespSaleRepayPlanVo data = saleApply.getData();
                if (!"0000".equals(data.getResponseCode())) {
                    log.warn("查询API赊销账单 赊销单号：{} 资方返回结果错误：{}", orderData.getSaleNo(), data.getResponseMsg());
                    return;
                }
                List<RepayPlanListVo> repayPlanList = data.getRepayPlanList();
                if (!CollectionUtil.isEmpty(repayPlanList)) {
                    Map<String, RepayPlanListVo> collect = formatPlan(repayPlanList);
                    List<SaleSchedule> scheduleList = saleScheduleService.list(new LambdaQueryWrapper<SaleSchedule>().eq(SaleSchedule::getSaleOrderId, orderData.getId()));
                    BigDecimal bigDecimal = updateSale(scheduleList, collect);
                    totalAmount.add(bigDecimal);
                }
            });
        }
        BigDecimal saleRepayAmount = totalAmount.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
        DisburseData up = new DisburseData();
        up.setId(disburseData.getId());
        up.setSaleRepayAmount(saleRepayAmount);
        disburseDataMapper.updateById(up);
        return ResponseResult.success();
    }

    private Map<String, RepayPlanListVo> formatPlan(List<RepayPlanListVo> repayPlanList) {
        return repayPlanList.stream().collect(Collectors.toMap(RepayPlanListVo::getRepayTerm, V -> V));
    }


    public BigDecimal updateSale(List<SaleSchedule> scheduleList,Map<String, RepayPlanListVo> collect) {
        BigDecimal saleRepayAmount = new BigDecimal("0.00");
        List<SaleSchedule> upData = new ArrayList<>(scheduleList.size());
        for (SaleSchedule schedule : scheduleList) {
            SaleSchedule upSchedule = new SaleSchedule();
            upSchedule.setId(schedule.getId());
            String repayTerm = schedule.getRepayTerm();
            if (collect.containsKey(repayTerm)) {
                RepayPlanListVo vo = collect.get(repayTerm);
                upSchedule.setTotalAmt(new BigDecimal(vo.getTotalAmt()));
                upSchedule.setTermRetPrin(new BigDecimal(vo.getTermRetPrin()));
                upSchedule.setDeratePrin(NumberUtil.safeParseBigDecimal(vo.getDeratePrin()));
                upSchedule.setVipDeratePrin(NumberUtil.safeParseBigDecimal(vo.getVipDeratePrin()));
                // 本期应还利息
                upSchedule.setTermRetInt(BigDecimal.ZERO);
                // 本期应还罚息
                upSchedule.setTermRetFint(BigDecimal.ZERO);
                upSchedule.setSettleFlag("1".equals(vo.getStatus()) ? "CLOSE" : "RUNNING");
                if (ObjectUtil.isNotEmpty(vo.getRepaySuccTime())) {
                    upSchedule.setDatePay(vo.getRepaySuccTime().substring(0, 10));
                    upSchedule.setDatePayTime(vo.getRepaySuccTime());
                }
                if(schedule.getRepayApplyNo() == null){
                    upSchedule.setRepayApplyNo(vo.getRepayApplyNo());
                }
            } else {
                upSchedule.setTotalAmt(BigDecimal.ZERO);
                upSchedule.setTermRetPrin(BigDecimal.ZERO);
                // 本期应还利息
                upSchedule.setTermRetInt(BigDecimal.ZERO);
                // 本期应还罚息
                upSchedule.setTermRetFint(BigDecimal.ZERO);
                upSchedule.setDeratePrin(BigDecimal.ZERO);
                upSchedule.setVipDeratePrin(BigDecimal.ZERO);

                upSchedule.setSettleFlag("CLOSE");
            }
            saleRepayAmount = saleRepayAmount.add(upSchedule.getTotalAmt());
            upData.add(upSchedule);
        }
        saleScheduleService.updateBatchById(upData);
        return saleRepayAmount;
    }
}