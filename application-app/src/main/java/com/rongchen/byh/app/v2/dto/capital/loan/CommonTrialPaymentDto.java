package com.rongchen.byh.app.v2.dto.capital.loan;

import com.rongchen.byh.common.api.zifang.vo.RepayPlanCalcVo;
import com.rongchen.byh.common.core.object.ResponseResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 试算dto
 * @date 2025/4/24 16:45:40
 */
@Data
public class CommonTrialPaymentDto {

    @Schema(description = "金额")
    private BigDecimal amount;

    @Schema(description = "期数")
    private String period;

    @Schema(description = "资方id")
    private Long capitalId;

    ResponseResult<RepayPlanCalcVo> result;
}
