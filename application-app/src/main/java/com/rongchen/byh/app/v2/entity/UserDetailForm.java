package com.rongchen.byh.app.v2.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.rongchen.byh.app.v2.dto.CommonDto;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@TableName(value = "user_detail_form")
@Data
public class UserDetailForm implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 字段名称
     */
    private String title;
    /**
     * 字段选项
     */
    private String optionValue;
    /**
     * 选项框类型
     */
    private Integer optionType;
    /**
     * 回传的值类型
     */
    private String filedType;
    /**
     * 回传的字段名
     */
    private String filedName;
    /**
     * 提示词
     */
    private String hint;
    /**
     * 最大长度
     */
    private Integer maxLength;

    /**
     * 是否是必须值
     */
    private String required;
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 选项值集合
     */
    @TableField(exist = false)
    private List<CommonDto> optionList;

    /**
     * 返回参数集合
     */
    @TableField(exist = false)
    private List<CommonDto> filedList;
}