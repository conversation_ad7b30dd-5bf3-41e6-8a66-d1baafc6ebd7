package com.rongchen.byh.app.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.app.entity.DisburseData;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 项目名称：byh_java
 * 文件名称: DisburseDataMapper
 * 创建时间: 2025-04-05 14:14
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.app.dao
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
public interface DisburseDataMapper extends BaseMapper<DisburseData> {

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(DisburseData record);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(DisburseData record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(DisburseData record);

    int updateBatch(@Param("list") List<DisburseData> list);

    int updateBatchSelective(@Param("list") List<DisburseData> list);

    int batchInsert(@Param("list") List<DisburseData> list);

    int batchInsertSelectiveUseDefaultForNull(@Param("list") List<DisburseData> list);

    int batchInsertOrUpdate(@Param("list") List<DisburseData> list);

    DisburseData selectByUserId(Long userId);

    DisburseData selectByLoanNo(String loanNo);

    DisburseData selectByCreditNo(String creditNo);

    List<DisburseData> selectListByStatus(int status);

    DisburseData selectNewByUser(DisburseData disburseData);

    List<DisburseData> selectListByStatusAndDay(int status, int day);
}