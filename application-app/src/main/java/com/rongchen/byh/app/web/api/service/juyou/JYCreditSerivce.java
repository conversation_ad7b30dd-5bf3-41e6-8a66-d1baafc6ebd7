package com.rongchen.byh.app.web.api.service.juyou;

import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.exceptions.ExternalApiException;
import com.rongchen.byh.app.web.api.vo.juyou.req.JYCreditNoticeResultDto;
import com.rongchen.byh.app.web.api.vo.juyou.res.JYCreditQueryLimitVo;
import com.rongchen.byh.app.web.api.vo.juyou.res.JYCreditQueryResultVo;
import org.springframework.stereotype.Service;

@Service
public interface JYCreditSerivce {
    /**
     * 用户进件授信
     * @param result
     * @return
     */
    JSONObject creditApply(JSONObject result, String channel) throws ExternalApiException;

    /**
     * 授信结果查询
     * @param creditApplyNo
     * @return
     */
    JYCreditQueryResultVo creditQueryResult(String creditApplyNo, String channel);

    /**
     * 用户额度查询接口
     * @param creditApplyNo
     * @return
     */
    JYCreditQueryLimitVo creditQueryLimit(String creditApplyNo, String channel);

    /**
     * 授信通知接口
     * @param creditApplyNo
     * @return
     */
    JYCreditNoticeResultDto creditNoticeResult(String creditApplyNo);

    JSONObject buildResult(JSONObject result);
}
