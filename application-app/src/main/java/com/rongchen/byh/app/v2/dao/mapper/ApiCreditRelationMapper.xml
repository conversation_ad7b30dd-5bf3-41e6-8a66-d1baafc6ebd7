<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.v2.dao.ApiCreditRelationMapper">

    <select id="selectByPushRecordId" resultType="com.rongchen.byh.app.v2.entity.ApiCreditRelation">
        select * from api_credit_relation where api_push_record_id = #{pushRecordId} order by id desc limit 1
    </select>
    <select id="selectByApplyId" resultType="com.rongchen.byh.app.v2.entity.ApiCreditRelation">
        select * from api_credit_relation where user_loan_apply_id = #{applyId} order by id desc limit 1
    </select>
    <select id="selectInProcessList" resultType="com.rongchen.byh.app.v2.entity.ApiCreditRelation">
        select * from api_credit_relation where status = 1 and create_time >= DATE_SUB(current_date(), INTERVAL 1 day)
    </select>
</mapper>

