package com.rongchen.byh.app.v2.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 用户资方授信结果记录表
 * @TableName user_capital_credit_record
 */
@TableName(value ="user_capital_credit_record")
@Data
public class UserCapitalCreditRecord implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户授信结果表id
     */
    private Long resultId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 资方id
     */
    private Long capitalId;

    /**
     * 授信进件单号
     */
    private String orderNo;

    /**
     * 授信状态 1-授信中 2-授信成功 3-授信失败 4-授信失效
     */
    private Integer creditStatus;

    /**
     * 资方授信金额
     */
    private BigDecimal creditAmount;

    /**
     * 授信完成时间
     */
    private Date creditTime;

    /**
     * 授信来源
     */
    private Integer sourceFrom;

    /**
     * 失败原因
     */
    private String failReason;

    /**
     * 删除标识 0-正常 1-删除
     */
    private Integer deleted;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}