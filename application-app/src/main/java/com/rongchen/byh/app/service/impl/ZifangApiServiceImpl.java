package com.rongchen.byh.app.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.app.config.ThreadConfig;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.dao.RepaySaleApplyMapper;
import com.rongchen.byh.app.dao.RepayScheduleApplyMapper;
import com.rongchen.byh.app.dao.RepayScheduleMapper;
import com.rongchen.byh.app.dao.SaleScheduleMapper;
import com.rongchen.byh.app.dao.UserCreditDataMapper;
import com.rongchen.byh.app.dto.api.CreditNoticeDto;
import com.rongchen.byh.app.dto.api.LoanNoticeDto;
import com.rongchen.byh.app.dto.api.RepayNoticeDto;
import com.rongchen.byh.app.dto.api.SaleNoticeDto;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.entity.RepaySaleApply;
import com.rongchen.byh.app.entity.RepaySchedule;
import com.rongchen.byh.app.entity.RepayScheduleApply;
import com.rongchen.byh.app.entity.SaleSchedule;
import com.rongchen.byh.app.entity.UserCreditData;
import com.rongchen.byh.app.job.SaleSchduleJob;
import com.rongchen.byh.app.job.SyncRepayJob;
import com.rongchen.byh.app.job.SyncSaleRepayJob;
import com.rongchen.byh.app.service.*;
import com.rongchen.byh.common.api.zifang.vo.BaseVo;
import com.rongchen.byh.common.core.constant.SettleFlagConstant;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import com.rongchen.byh.common.rabbitmq.dto.LoanSuccessDto;
import com.rongchen.byh.common.rabbitmq.dto.SaleDto;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ZifangApiServiceImpl implements ZifangApiService {

    @Resource
    DisburseDataMapper disburseDataMapper;
    @Resource
    LoanService loanService;
    @Resource
    UserCreditDataMapper userCreditDataMapper;

    @Resource
    CallCrmService callCrmService;
    @Resource
    CreditService creditService;
    @Resource
    SmsLoanService smsLoanService;
    @Resource
    RabbitTemplate rabbitTemplate;
    @Resource
    private RepaySaleApplyMapper repaySaleApplyMapper;
    @Resource
    private RepayScheduleMapper repayScheduleMapper;
    @Resource
    SaleScheduleMapper saleScheduleMapper;
    @Resource
    DisburseRecordService disburseRecordService;
    @Resource
    RepayScheduleApplyMapper repayScheduleApplyMapper;
    @Resource
    DisburseService disburseService;
    @Resource
    RedissonClient redissonClient;
    @Resource
    private RepayPlanUpdateService asyncRepayPlanUpdateService;
    @Resource
    SyncRepayJob syncRepayJob;
    @Resource
    SaleSchduleJob saleSchduleJob;

    @Override
    public BaseVo creditNotice(CreditNoticeDto creditNoticeDto) {
        RLock lock = redissonClient.getLock("credit_notice:" + creditNoticeDto.getCreditNo());
        BaseVo baseVo = new BaseVo();
        try {
            boolean locked = lock.tryLock(10, 30, TimeUnit.SECONDS);
            if (!locked) {
                baseVo.setResponseCode("9999");
                baseVo.setResponseMsg("系统繁忙，请稍后重试");
                return baseVo;
            }
            DisburseData data = disburseDataMapper.selectByCreditNo(creditNoticeDto.getCreditNo());
            if (creditNoticeDto.getApprovalResult().equals("01")) {
                ResponseResult<Void> result = creditService.loanApply(data.getUserId());
                if (result.isSuccess()) {
                    data.setCreditStatus(300);
                } else {
                    data.setCreditStatus(400);
                    UserCreditData creditData = userCreditDataMapper.queryByUserId(data.getUserId());
                    creditData.setResidueAmount(creditData.getResidueAmount().add(data.getCreditAmount()));
                    creditData.setFreezeAmount(creditData.getFreezeAmount().subtract(data.getCreditAmount()));
                    userCreditDataMapper.updateById(creditData);
                }
                data.setCreditTime(DateUtil.date());
                disburseDataMapper.updateById(data);
            } else if (creditNoticeDto.getApprovalResult().equals("02")) {
                data.setCreditStatus(200);
                data.setCreditTime(DateUtil.date());
                disburseDataMapper.updateById(data);
                UserCreditData creditData = userCreditDataMapper.queryByUserId(data.getUserId());
                creditData.setResidueAmount(creditData.getResidueAmount().add(data.getCreditAmount()));
                creditData.setFreezeAmount(creditData.getFreezeAmount().subtract(data.getCreditAmount()));
                userCreditDataMapper.updateById(creditData);
            }
            baseVo.setResponseCode("0000");
            baseVo.setResponseMsg("成功");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            baseVo.setResponseCode("9999");
            baseVo.setResponseMsg("系统异常");
            log.error("处理授信通知异常", e);
        } finally {
            // Release the lock
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return baseVo;
    }

    @Override
    public BaseVo loanNotice(LoanNoticeDto loanNoticeDto) {
        BaseVo baseVo = new BaseVo();
        baseVo.setResponseCode("0000");
        baseVo.setResponseMsg("成功");
        String loanNo = loanNoticeDto.getLoanNo();
        String fundCode = loanNoticeDto.getFundCode();

        // Get Redisson distributed lock
        RLock lock = redissonClient.getLock("loan_notice:" + loanNo);
        try {
            // Try to acquire lock with 10 seconds wait time and 30 seconds lease time
            boolean locked = lock.tryLock(10, 30, TimeUnit.SECONDS);
            if (!locked) {
                baseVo.setResponseCode("9999");
                baseVo.setResponseMsg("系统繁忙，请稍后重试");
                return baseVo;
            }

            DisburseData disburseData = disburseDataMapper.selectByLoanNo(loanNo);
            if (disburseData.getCreditStatus() != 300) {
                baseVo.setResponseCode("9999");
                baseVo.setResponseMsg("状态不正确");
                return baseVo;
            }

            String loanResult = loanNoticeDto.getLoanResult();
            DisburseData upEntity = new DisburseData();
            upEntity.setId(disburseData.getId());

            // 放款失败
            if ("04".equals(loanResult)) {
                upEntity.setCreditStatus(400);
                disburseDataMapper.updateById(upEntity);
                UserCreditData userCreditData = userCreditDataMapper.queryByUserId(disburseData.getUserId());
                UserCreditData upCreditData = new UserCreditData();
                upCreditData.setId(userCreditData.getId());
                BigDecimal subtract = userCreditData.getFreezeAmount().subtract(disburseData.getCreditAmount());
                upCreditData.setFreezeAmount(subtract);
                BigDecimal add = userCreditData.getResidueAmount().add(disburseData.getCreditAmount());
                upCreditData.setResidueAmount(add);
                userCreditDataMapper.updateById(upCreditData);
            } else if ("06".equals(loanResult)) {
                upEntity.setCreditStatus(500);
                disburseData.setFundCode(fundCode);
                disburseData.setLoanTime(DateUtil.parse(loanNoticeDto.getLoanTime()));
                upEntity.setFundCode(fundCode);
                upEntity.setLoanTime(DateUtil.parse(loanNoticeDto.getLoanTime()));
                upEntity.setContractId(loanNoticeDto.getContractId());
                upEntity.setFundOrderId(loanNoticeDto.getFundOrderId());
                disburseDataMapper.updateById(upEntity);

                UserCreditData userCreditData = userCreditDataMapper.queryByUserId(disburseData.getUserId());
                UserCreditData upCreditData = new UserCreditData();
                upCreditData.setId(userCreditData.getId());
                BigDecimal subtract = userCreditData.getFreezeAmount().subtract(disburseData.getCreditAmount());
                upCreditData.setFreezeAmount(subtract);
                BigDecimal add = userCreditData.getWithdrawAmount().add(disburseData.getCreditAmount());
                upCreditData.setWithdrawAmount(add);
                userCreditDataMapper.updateById(upCreditData);

                Map<String, String> contextMap = MDC.getCopyOfContextMap();
                String traceId = contextMap.getOrDefault("traceId", DateUtil.now());

                LoanSuccessDto loanSuccessDto = new LoanSuccessDto();
                loanSuccessDto.setLoanNo(loanNo);
                loanSuccessDto.setTraceId(traceId);
                rabbitTemplate.convertAndSend(QueueConstant.LOAN_SUCCESS_QUEUE, JSONObject.toJSONString(loanSuccessDto));
            } else {
                baseVo.setResponseCode("9999");
                baseVo.setResponseMsg("状态不正确2");
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            baseVo.setResponseCode("9999");
            baseVo.setResponseMsg("系统异常");
            log.error("处理放款通知异常", e);
        } finally {
            // Release the lock
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

        return baseVo;
    }

    @Override
    public BaseVo repayNotice(RepayNoticeDto repayNoticeDto) {
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        String traceId = contextMap.getOrDefault("traceId", DateUtil.now());

        BaseVo baseVo = new BaseVo();
        // 还款流水号
        String repayApplyNo = repayNoticeDto.getRepayApplyNo();

        List<RepayScheduleApply> repayScheduleApplyList = repayScheduleApplyMapper
                .selectList(new LambdaQueryWrapper<RepayScheduleApply>()
                        .eq(RepayScheduleApply::getRepayApplyNo, repayApplyNo));
        // 还款申请不存在
        if (CollUtil.isEmpty(repayScheduleApplyList)) {
            log.info("【资方】 还款结果通知 还款流水号不存在，还款流水号：{}", repayApplyNo);
            baseVo.setResponseCode("9999");
            baseVo.setResponseMsg("还款流水号不存在");
            return baseVo;
        }
        // 还款申请存在多个
        if (repayScheduleApplyList.size() > 1) {
            log.error("【资方】 还款结果通知 还款流水号存在多个，还款流水号：{}", repayApplyNo);
        }

        RepaySchedule repaySchedule = repayScheduleMapper.selectByRepayApplyNo(repayApplyNo);
        if (repaySchedule == null) {
            log.info("【资方】 还款结果通知 账单不存在，还款流水号：{}", repayApplyNo);
            baseVo.setResponseCode("9999");
            baseVo.setResponseMsg("还款流水号不存在");
            return baseVo;
        }
        if ("RUNNING".equals(repaySchedule.getSettleFlag())) {
            baseVo.setResponseCode("0000");
            baseVo.setResponseMsg("成功");
            return baseVo;
        } else if ("CLOSE".equals(repaySchedule.getSettleFlag())) {
            baseVo.setResponseCode("0000");
            baseVo.setResponseMsg("成功");
            return baseVo;
        }
        RepaySchedule upRepayData = new RepaySchedule();
        upRepayData.setId(repaySchedule.getId());
        String status = repayNoticeDto.getStatus();

        // 还款失败
        if (StrUtil.equals(status, "FAIL")) {
            if (repayScheduleApplyList != null && !repayScheduleApplyList.isEmpty()) {
                for (RepayScheduleApply apply : repayScheduleApplyList) {
                    apply.setRepayStatus(2);
                    apply.setReason(repayNoticeDto.getResult());
                    apply.setResponseTime(DateUtil.date());
                }
            }
            disburseRecordService.saveRecord("账单(repaySchedule)id:" + repaySchedule.getId(), traceId, "还款结果通知");
            upRepayData.setSettleFlag(SettleFlagConstant.RUNNING);
            log.info("【资方】 还款结果通知 还款失败，用户id：{}，还款流水号：{},失败原因：{}", repaySchedule.getUserId(), repayApplyNo,
                    repayNoticeDto.getResult());
        } else if (StrUtil.equals(status, "SUCCESS")) {
            // 还款成功
            if (repayScheduleApplyList != null && !repayScheduleApplyList.isEmpty()) {
                for (RepayScheduleApply apply : repayScheduleApplyList) {
                    apply.setRepayStatus(1);
                    apply.setResponseTime(DateUtil.date());
                }
            }

            upRepayData.setSettleFlag(SettleFlagConstant.CLOSE);
            upRepayData.setDatePay(DateUtil.today());
            upRepayData.setDatePayTime(repayNoticeDto.getRepayTime());
        }
        repayScheduleMapper.updateById(upRepayData);

        if (repayScheduleApplyList != null && !repayScheduleApplyList.isEmpty()) {
            for (RepayScheduleApply apply : repayScheduleApplyList) {
                repayScheduleApplyMapper.updateById(apply);
            }
        }
        int repayTerm = Integer.parseInt(repaySchedule.getRepayTerm());
        if (StrUtil.equals(status, "SUCCESS")) {
            // 发送扣赊销部分消息
            if (repayTerm <= 3) {
                SaleDto saleDto = new SaleDto();
                saleDto.setDisburseId(repaySchedule.getDisburseId());
                saleDto.setTerm(repaySchedule.getRepayTerm());
                saleDto.setTraceId(traceId);
                if (repayScheduleApplyList != null && !repayScheduleApplyList.isEmpty()) {
                    RepayScheduleApply repayScheduleApply = repayScheduleApplyList.get(0);
                    saleDto.setRepayType(repayScheduleApply.getRepayType());
                }

                rabbitTemplate.convertAndSend(QueueConstant.SALE_QUEUE, JSONObject.toJSONString(saleDto));
            } else {
                if (checkSendSms(repayNoticeDto.getRepayTime(), repaySchedule.getRepayOwneDate())) {
                    // 发送短信
                    CompletableFuture.runAsync(() -> {
                        MDC.setContextMap(contextMap);
                        try {
                            smsLoanService.sendRepaySms(repaySchedule.getDisburseId(), repaySchedule.getRepayTerm());
                        } catch (Exception e) {
                            log.error("【资方】 还款结果通知 发送扣款失败短信失败，用户id：{}，还款流水号：{},失败原因：", repaySchedule.getUserId(),
                                    repayApplyNo, e);
                        }

                    }, ThreadConfig.threadPoolExecutor());
                }
            }
            try {
                // 同步资方账单
                syncRepayJob.syncProcessV2(disburseDataMapper.selectById(repaySchedule.getDisburseId()));
            } catch (Exception e) {
                log.warn("【资方】 还款结果通知 同步资方账单异常",e);
            }
            // 校验只用是否完结
            disburseService.checkOverDisburse(repaySchedule.getDisburseId());
        } else if (StrUtil.equals(status, "FAIL")) {
            // 发送扣款失败短信 主动提交还款 自动扣款
            if (sendMobileFlag(repaySchedule.getUserId())) {
                CompletableFuture.runAsync(() -> {
                    MDC.setContextMap(contextMap);
                    try {
                        smsLoanService.sendRepayFailSms(repaySchedule.getDisburseId(), repaySchedule.getId(),
                                repaySchedule.getUserId(), 1);
                    } catch (Exception e) {
                        log.error("【资方】 还款结果通知 发送扣款失败短信失败，用户id：{}，还款流水号：{},失败原因：", repaySchedule.getUserId(),
                                repayApplyNo, e);
                    }

                }, ThreadConfig.threadPoolExecutor());
            }
        }

        try {
            long startTime = System.currentTimeMillis();
            log.info("【账单列表】触发异步还款计划更新检查，userId: {}", repaySchedule.getUserId());
            asyncRepayPlanUpdateService.triggerRepayPlanUpdate(repaySchedule.getUserId());
            long endTime = System.currentTimeMillis();
            log.info("【账单列表】异步还款计划更新检查完成，userId: {}, 耗时: {}ms", repaySchedule.getUserId(), endTime - startTime);
        } catch (Exception e) {
            log.error("【账单列表】提交异步还款计划更新任务失败，userId: {}, error: ", repaySchedule.getUserId(), e);
        }

        baseVo.setResponseCode("0000");
        baseVo.setResponseMsg("成功");
        return baseVo;
    }

    public boolean sendMobileFlag(Long userId) {

        return true;
    }

    /**
     * 判断是否需要发送短信
     * 
     * @param repayTime   实际还款时间
     * @param needPayTime 自动扣款最后时间
     * @return true-发送短信 false-不发送短信
     */
    private boolean checkSendSms(String repayTime, String needPayTime) {
        needPayTime = needPayTime + " 23:59:59";
        DateTime repayDate = DateUtil.parse(repayTime);
        DateTime needDate = DateUtil.parse(needPayTime);
        long time = repayDate.getTime(); // 实际还款的时间戳
        long needTime = needDate.getTime(); // 自动扣款结束的时间戳
        // 自动扣款结束 小于 实际还款时间，说明是逾期还款，则不发短信
        if (needTime < time) {
            return false;
        }
        // 自动扣款结束 大于 实际还款时间，说明是自动扣款或者主动还款，则发短信
        return true;
    }

    @Override
    public BaseVo saleNotice(SaleNoticeDto saleNoticeDto) {
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        String traceId = contextMap.getOrDefault("traceId", DateUtil.now());

        BaseVo baseVo = new BaseVo();
        String repayApplyNo = saleNoticeDto.getRepayApplyNo();

        RepaySaleApply repaySaleApply = repaySaleApplyMapper.selectByRepayApplyNo(repayApplyNo);

        // 查询账单信息
        SaleSchedule saleSchedule = saleScheduleMapper.selectByRepayApplyNo(repayApplyNo);
        if (saleSchedule == null) {
            log.info("【资方】 赊销结果通知 赊销账单不存在，还款流水号：{}", repayApplyNo);
            baseVo.setResponseCode("9999");
            baseVo.setResponseMsg("还款流水号不存在");
            return baseVo;
        }

        if ("RUNNING".equals(saleSchedule.getSettleFlag())) {
            baseVo.setResponseCode("0000");
            baseVo.setResponseMsg("成功");
            return baseVo;
        } else if ("CLOSE".equals(saleSchedule.getSettleFlag())) {
            baseVo.setResponseCode("0000");
            baseVo.setResponseMsg("成功");
            return baseVo;
        }

        SaleSchedule upSaleData = new SaleSchedule();
        upSaleData.setId(saleSchedule.getId());
        String status = saleNoticeDto.getStatus();
        // 还款失败
        if (StrUtil.equals(status, "FAIL")) {

            if (repaySaleApply != null) {
                repaySaleApply.setRepayStatus(2);
                repaySaleApply.setReason(saleNoticeDto.getResult());
                repaySaleApply.setResponseTime(DateUtil.date());
            }

            disburseRecordService.saveRecord("赊销(saleSchedule)id:" + saleSchedule.getId(), traceId, "还款结果通知");
            upSaleData.setSettleFlag(SettleFlagConstant.RUNNING);
            log.info("【资方】 还款结果通知 还款失败，用户id：{}，还款流水号：{},失败原因：{}", saleSchedule.getUserId(), repayApplyNo,
                    saleNoticeDto.getResult());
        } else if (StrUtil.equals(status, "SUCCESS")) {
            // 还款成功

            if (repaySaleApply != null) {
                repaySaleApply.setRepayStatus(1);
                repaySaleApply.setResponseTime(DateUtil.date());
            }

            upSaleData.setSettleFlag(SettleFlagConstant.CLOSE);
            upSaleData.setDatePay(DateUtil.today());
            upSaleData.setDatePayTime(saleNoticeDto.getRepayTime());
        }
        saleScheduleMapper.updateById(upSaleData);

        if (StrUtil.equals(status, "SUCCESS")) {
            if (checkSendSms(saleNoticeDto.getRepayTime(), saleSchedule.getRepayOwneDate())) {
                // 发送短信
                CompletableFuture.runAsync(() -> {
                    MDC.setContextMap(contextMap);
                    try {
                        smsLoanService.sendRepaySms(saleSchedule.getDisburseId(), saleSchedule.getRepayTerm());
                    } catch (Exception e) {

                    }

                }, ThreadConfig.threadPoolExecutor());

            }
            // 同步赊销账单
            try {
                saleSchduleJob.queryApiSaleSchedule(disburseDataMapper.selectById(saleSchedule.getDisburseId()));
            } catch (Exception e) {
                log.warn("【资方】 还款结果通知 同步赊销账单异常",e);
            }
        }

        if (repaySaleApply != null) {
            repaySaleApplyMapper.updateById(repaySaleApply);
        }

        baseVo.setResponseCode("0000");
        baseVo.setResponseMsg("成功");
        return baseVo;
    }
}
