package com.rongchen.byh.app.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.dao.UserCreditDataMapper;
import com.rongchen.byh.app.dto.api.CreditNoticeDto;
import com.rongchen.byh.app.dto.api.LoanNoticeDto;
import com.rongchen.byh.app.dto.api.RepayNoticeDto;
import com.rongchen.byh.app.dto.api.SaleNoticeDto;
import com.rongchen.byh.app.entity.*;
import com.rongchen.byh.app.service.ZifangApiService;
import com.rongchen.byh.app.v2.dao.ApiDisburseRelationMapper;
import com.rongchen.byh.app.v2.dao.UserCapitalCreditRecordMapper;
import com.rongchen.byh.app.v2.entity.ApiDisburseRelation;
import com.rongchen.byh.app.v2.entity.UserCapitalCreditRecord;
import com.rongchen.byh.common.api.zifang.vo.BaseVo;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import com.rongchen.byh.common.rabbitmq.dto.LoanSuccessDto;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@Service
@Slf4j
public class NewZifangApiServiceImpl implements ZifangApiService {


    @Resource
    RedissonClient redissonClient;
    @Resource
    UserCapitalCreditRecordMapper userCapitalCreditRecordMapper;
    @Resource(name = "zifangApiServiceImpl")
    ZifangApiServiceImpl zifangApiServiceImpl;

    @Override
    public BaseVo creditNotice(CreditNoticeDto creditNoticeDto) {
        RLock lock = redissonClient.getLock("credit_notice:" + creditNoticeDto.getCreditNo());
        BaseVo baseVo = new BaseVo();
        try {
            boolean locked = lock.tryLock(10, 30, TimeUnit.SECONDS);
            if (!locked) {
                baseVo.setResponseCode("9999");
                baseVo.setResponseMsg("系统繁忙，请稍后重试");
                return baseVo;
            }

            UserCapitalCreditRecord record = userCapitalCreditRecordMapper.selectByOrderNo(creditNoticeDto.getCreditNo());
            if (record == null) {
                baseVo.setResponseCode("9999");
                baseVo.setResponseMsg("订单号不存在");
                return baseVo;
            }

            if (creditNoticeDto.getApprovalResult().equals("01")) {
                record.setCreditStatus(2);
                record.setCreditAmount(BigDecimal.valueOf(creditNoticeDto.getTotalCredit()));
            } else if (creditNoticeDto.getApprovalResult().equals("02")) {
                record.setCreditStatus(3);
                record.setFailReason(creditNoticeDto.getApprovalResultDesc());
            } else {
                record.setCreditStatus(4);
                record.setFailReason("资方状态错误");
            }
            record.setCreditTime(DateUtil.date());
            userCapitalCreditRecordMapper.updateById(record);
            baseVo.setResponseCode("0000");
            baseVo.setResponseMsg("成功");
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            baseVo.setResponseCode("9999");
            baseVo.setResponseMsg("系统异常");
            log.error("处理授信通知异常", e);
        } finally {
            // Release the lock
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        return baseVo;
    }


    @Override
    public BaseVo loanNotice(LoanNoticeDto loanNoticeDto) {
        return zifangApiServiceImpl.loanNotice(loanNoticeDto);
    }


    @Override
    public BaseVo repayNotice(RepayNoticeDto repayNoticeDto) {
        return zifangApiServiceImpl.repayNotice(repayNoticeDto);
    }



    @Override
    public BaseVo saleNotice(SaleNoticeDto saleNoticeDto) {
        return zifangApiServiceImpl.saleNotice(saleNoticeDto);
    }

}
