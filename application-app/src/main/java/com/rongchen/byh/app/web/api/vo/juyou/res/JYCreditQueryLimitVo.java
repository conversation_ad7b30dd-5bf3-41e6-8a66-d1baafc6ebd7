package com.rongchen.byh.app.web.api.vo.juyou.res;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class JYCreditQueryLimitVo {
    @Schema(description = "授信申请流水号，调用方申请号")
    private String creditApplyNo;

    @Schema(description = "授信状态，见附录二：授信状态")
    private String creditStatus;

    @Schema(description = "授信总额度，单位：元，授信成功必传")
    private Integer creditLimit;

    @Schema(description = "额度类型")
    private String limitType;

    @Schema(description = "可用额度")
    private BigDecimal creditLimitSurplus;

    @Schema(description = "额度开始日期，格式 yyyyMMdd")
    private String creditStartDate;

    @Schema(description = "额度失效日期，格式 yyyyMMdd")
    private String creditEndDate;

    @Schema(description = "可选金额周期，授信成功必传")
    private List<AmountOption> amountOption;

    @Schema(description = "定价年利率，通过必传，例如：28.00")
    private BigDecimal yearRate;

    @Data
    public static class AmountOption {
        @Schema(description = "最小借款金额")
        private int min;

        @Schema(description = "最大借款金额")
        private int max;

        @Schema(description = "金额步长")
        private int step;

        @Schema(description = "可选借款周期")
        private String periods;
    }

}