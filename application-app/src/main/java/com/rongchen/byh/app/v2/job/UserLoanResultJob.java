package com.rongchen.byh.app.v2.job;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.v2.dao.ApiPushRecordMapper;
import com.rongchen.byh.app.v2.entity.ApiPushRecord;
import com.rongchen.byh.app.v2.vo.ApiPushRecordVo;
import com.rongchen.byh.app.web.api.adapter.AbstractChannelAdapter;
import com.rongchen.byh.app.web.api.adapter.ChannelAdapterFactory;
import com.rongchen.byh.app.web.api.common.ApiOperation;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
 * @version 1.0
 * @description 流量方借款结果通知定时任务
 * @date 2025/5/6 10:25
 */
@Component
@Slf4j
public class UserLoanResultJob {
    @Resource
    private ApiPushRecordMapper apiPushRecordMapper;

    @Resource(name = "taskExecutor")
    private Executor taskExecutor;

    @Resource
    private ChannelAdapterFactory adapterFactory;

    @XxlJob("userLoanResultNoticeJob")
    public void userLoanResultNoticeJob() {
        try {
            String jobTraceId = MDCUtil.generateTraceId();
            MDCUtil.setTraceId(jobTraceId);
            log.info("用户借款结果通知定时任务开始执行");
            ApiPushRecord apiPushRecord = new ApiPushRecord();
            apiPushRecord.setDisburseNotify(0);
            List<ApiPushRecordVo> pushRecordList = apiPushRecordMapper.selectNoNoticeList(apiPushRecord);
            // 查询还未通知的数据
            if (CollUtil.isEmpty(pushRecordList)) {
                log.info("用户借款结果通知定时任务没有查询到需要通知的数据");
                return;
            }
            int batchSize = 10;
            int totalRequests = pushRecordList.size();

            List<ApiPushRecordVo> failList = new ArrayList<>();

            // 查找对应策略
            for (int i = 0; i < totalRequests; i += batchSize) {
                int end = Math.min(i + batchSize, totalRequests);
                List<ApiPushRecordVo> batch = pushRecordList.subList(i, end);

                log.info("处理第 {} 批数据，", i);
                // 并行处理当前批次的请求
                List<CompletableFuture<?>> futures = batch.stream()
                        .map(data -> CompletableFuture.supplyAsync(
                                () -> {
                                    String newTranceId = MDCUtil.generateTraceId();
                                    MDCUtil.setTraceId(newTranceId);
                                    Optional<AbstractChannelAdapter> adapter = adapterFactory.getAdapter(data.getChannelCode(), ApiOperation.NOTIFY_LOAN_RESULT);
                                    if (adapter.isPresent()) {
                                        AbstractChannelAdapter abstractChannelAdapter = adapter.get();
                                        try {
                                            return abstractChannelAdapter.loanResultNotice(data.getChannelCode(), (JSONObject) JSONObject.toJSON(data));
                                        } catch (Exception e) {
                                            log.error("用户借款结果通知调用失败，订单id：{}", data.getId(), e);
                                        }
                                    }
                                    return ResponseResult.error(ErrorCodeEnum.FAIL);
                                },  taskExecutor).thenAccept(result -> {
                            if (!result.isSuccess()) {
                                failList.add(data);
                            }
                        }).exceptionally(e -> {
                            log.error("用户借款结果通知，订单id：{}", data.getId(), e);
                            failList.add(data);
                            return null;
                        }))
                        .collect(Collectors.toList());

                // 等待所有请求完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(100, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.error("用户借款结果通知定时任务执行异常", e);
        } finally {
            MDCUtil.clear();
        }
    }
}
