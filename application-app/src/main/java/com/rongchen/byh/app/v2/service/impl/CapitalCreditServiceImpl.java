package com.rongchen.byh.app.v2.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rongchen.byh.app.dao.CapitalDataMapper;
import com.rongchen.byh.app.dao.DisburseDataMapper;
import com.rongchen.byh.app.entity.CapitalData;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.v2.common.constants.CreditStatusConstant;
import com.rongchen.byh.app.v2.dao.UserCapitalCreditRecordMapper;
import com.rongchen.byh.app.v2.dao.UserCreditResultMapper;
import com.rongchen.byh.app.v2.dto.UserCapitalCreditApplyDto;
import com.rongchen.byh.app.v2.entity.UserCapitalCreditRecord;
import com.rongchen.byh.app.v2.entity.UserCreditResult;
import com.rongchen.byh.app.v2.service.ICapitalCreditService;
import com.rongchen.byh.app.v2.vo.CapitalRuleVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.MDCUtil;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Service
@Slf4j
public class CapitalCreditServiceImpl implements ICapitalCreditService {

    @Resource
    private UserCreditResultMapper userCreditResultMapper;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private CapitalDataMapper capitalDataMapper;
    @Resource
    private RabbitTemplate rabbitTemplate;


    /**
     * 用户资方授信提交
     * @param userId
     * @return
     */
    public ResponseResult<UserCreditResult> creditApply(Long userId) {
        RLock lock = redissonClient.getLock("creditApply:" + userId);
        try {
            boolean locked = lock.tryLock(5L, TimeUnit.SECONDS);
            if (!locked) {
                return ResponseResult.error(ErrorCodeEnum.FAIL,"获取锁失败");
            }
            // 1.查询是否有有效的授信记录，有则直接返回
            UserCreditResult result = userCreditResultMapper.selectByUserId(userId);
            if (result != null) {
                return ResponseResult.success(result);
            }
            // 2.无授信记录提交所有资方授信
            UserCreditResult entity = new UserCreditResult();
            entity.setUserId(userId);
            // 查询所有在线资方
            LambdaQueryWrapper<CapitalData> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CapitalData::getStatus,1);
            List<CapitalData> list = capitalDataMapper.selectList(queryWrapper);
            if (CollectionUtil.isEmpty(list)) {
                entity.setCreditStatus(CreditStatusConstant.CREDIT_FAILURE);
            } else {
                entity.setCreditStatus(CreditStatusConstant.CREDITING);
            }
            userCreditResultMapper.insert(entity);

            if (!CollectionUtil.isEmpty(list)) {
                list.forEach(li -> {
                    String traceId = MDCUtil.generateTraceId();
                    UserCapitalCreditApplyDto dto = new UserCapitalCreditApplyDto();
                    dto.setTraceId(traceId);
                    dto.setUserId(userId);
                    dto.setResultId(entity.getId());
                    dto.setCapitalId(li.getId());

                    log.info("用户资方授信提交 用户：{}, 资方id：{}，traceId:{}",userId,li.getId(),traceId);

                    rabbitTemplate.convertAndSend(QueueConstant.USER_CAPITAL_CREDIT_APPLY_QUEUE, JSONObject.toJSONString(dto));
                });
            }

            UserCreditResult selectById = userCreditResultMapper.selectById(entity.getId());
            return ResponseResult.success(selectById);
        } catch (Exception e) {
            log.error("用户资方授信提交异常 用户id：{}",userId,e);
            return ResponseResult.error(ErrorCodeEnum.FAIL,e.getMessage());
        }
    }

    @Override
    public ResponseResult<CapitalRuleVo> capitalRule(Long userId) {
        // 查询用户是否授信通过
        UserCreditResult result = userCreditResultMapper.selectByUserId(userId);
        if (result == null) {
            return ResponseResult.error(ErrorCodeEnum.FAIL,"用户未授信");
        }
        if (result.getCreditStatus().equals(CreditStatusConstant.CREDITING)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL,"用户授信中");
        }
        if (result.getCreditStatus().equals(CreditStatusConstant.CREDIT_FAILURE)) {
            return ResponseResult.error(ErrorCodeEnum.FAIL,"用户授信失败");
        }
        // 判断有无支用记录
        //  无支用-走资方排序等规则确认先后
        //  有支用-判断资方授信通过的，还有没有没有提交支用的
        Long capitalId = userCreditResultMapper.matchCapital(userId, result.getId());
        if (capitalId == null) {
            result.setCreditStatus(CreditStatusConstant.CREDIT_FAILURE);
            userCreditResultMapper.updateById(result);
            return ResponseResult.error(ErrorCodeEnum.FAIL,"用户路由失败");
        }
        CapitalRuleVo vo = new CapitalRuleVo();
        vo.setCapitalId(capitalId);
        return ResponseResult.success(vo);
    }

}
