package com.rongchen.byh.app.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.app.entity.UserData;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 项目名称：byh_java
 * 文件名称: UserDataMapper
 * 创建时间: 2025-04-24 19:04
 * 创建人: XuYu
 * 所属包名: com.rongchen.byh.app.dao
 * 文件描述: 
 * Copyright (c) 2025 byh_java All rights reserved.
 */
public interface UserDataMapper extends BaseMapper<UserData> {

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(UserData record);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(UserData record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(UserData record);

    int updateBatch(@Param("list") List<UserData> list);

    int updateBatchSelective(@Param("list") List<UserData> list);

    int batchInsert(@Param("list") List<UserData> list);

    int batchInsertOrUpdate(@Param("list") List<UserData> list);

    UserData queryByMobile(String mobile);

    Integer selectSaleRecordByMobile(String mobile);
}