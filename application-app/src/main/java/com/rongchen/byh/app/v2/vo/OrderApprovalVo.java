package com.rongchen.byh.app.v2.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;

import org.springframework.lang.Nullable;

import java.lang.reflect.Array;
import java.math.BigDecimal;

/**
 * 订单审批结果实体类
 */
@Data
public class OrderApprovalVo {

    /**
     * 订单编号
     */
    @NotNull(message = "订单编号不能为空")
    private String order_no;

    /**
     * 审批结论（10=通过，40=不通过）
     */
    @NotNull(message = "审批结论不能为空")
    private Integer conclusion;

    /**
     * 审批通过时间（10位时间戳）
     */
    private Long approval_time;

    /**
     * 流程标记（默认0，无特殊沟通不传）
     */
    private Integer process_flag = 0;

    /**
     * 审批金额是否固定（0=固定，默认0）
     */
    private Integer amount_type = 0;

    /**
     * 审批金额（本金，单位元）
     * - 参与利息、管理费计算的金额
     * - 保留2位小数
     */
    private Float approval_amount;

    /**
     * 期限类型（2=多期按月计息，默认2）
     */
    private Integer term_unit = 2;

    /**
     * 审批期限是否固定（0=固定，默认0）
     */
    private Integer term_type = 0;

    /**
     * 审批月数（固定期限）
     */
    private Integer approval_term;
    /**
     * 审批拒绝时间
     */
    private Long refuse_time;
    /**
     * 备注说明
     */
    private String remark;
    /**
     * 需重填的资料项
     * key说明:仅限于补充信息推送接口推送的用户填写项（抓取项除外）抓取项：contacts，is_simulator，device_info等
     * value(string类型)说明：枚举指['C01','C02','C03'].
     * C01:资料项为空（融360未传）.
     * C02:身份证图片模糊／姿势不对／超过有效期.
     * C03:信息不完整／不规范（如邮箱格式不对，地址不详细）
     */
    private Array supplements;

}