package com.rongchen.byh.app.v2.dao;

import com.rongchen.byh.app.v2.entity.UserCreditResult;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
* <AUTHOR>
* @description 针对表【user_credit_result(用户授信结果表)】的数据库操作Mapper
* @createDate 2025-04-23 14:23:29
* @Entity com.rongchen.byh.app.v2/entity.UserCreditResult
*/
@Mapper
public interface UserCreditResultMapper extends BaseMapper<UserCreditResult> {

    UserCreditResult selectByUserId(Long userId);

    @Select("select USER_CAPITAL_RULE(#{userId},#{resultId}) ")
    Long matchCapital(@Param("userId") Long userId,@Param("resultId") Long resultId);
}




