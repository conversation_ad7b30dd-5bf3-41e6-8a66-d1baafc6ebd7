package com.rongchen.byh.app.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import com.rongchen.byh.app.dao.*;
import com.rongchen.byh.app.dto.CreateTokenDto;
import com.rongchen.byh.app.dto.app.AppLoginDto;
import com.rongchen.byh.app.entity.*;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.app.v2.common.constants.CreditStatusConstant;
import com.rongchen.byh.app.v2.dao.UserCreditResultMapper;
import com.rongchen.byh.app.v2.entity.UserCreditResult;
import com.rongchen.byh.app.vo.app.ProcessStateVo;
import com.rongchen.byh.app.vo.app.RegisterUserVo;
import com.rongchen.byh.common.api.sms.service.SmsService;
import com.rongchen.byh.common.api.sms.vo.SendSmsVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.ContextUtil;
import com.rongchen.byh.common.core.util.IpUtil;
import com.rongchen.byh.common.core.util.MyModelUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName AppLoginController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/12/11 10:25
 * @Version 1.0
 **/

@Tag(name = "app登录页面用户相关接口")
@ApiSupport(order = 2)
@RestController
@RequestMapping("/userApi")
public class AppLoginController {

    @Resource
    UserDataMapper userDataMapper;
    @Resource
    UserDetailMapper userDetailMapper;
    @Resource
    UserLoanApplyMapper userLoanApplyMapper;
    @Resource
    DisburseDataMapper disburseDataMapper;
    @Resource
    UserCreditDataMapper userCreditDataMapper;
    @Resource
    RedissonClient redissonClient;
    @Resource
    SmsService smsService;
    @Value("${spring.profiles.active}")
    private String active;
    @Resource
    UserCreditResultMapper userCreditResultMapper;

    private static final String MOBILE_PREFIX = "mb:";
    private static final String IP_PREFIX = "ip:";
    private static final String YZM_PREFIX = "yzm:";

    private static final Map<String,String> map = new HashMap<>();

    static {
        map.put("18800000000","133133");
    }

    @Operation(summary = "APP用户登录")
    @PostMapping("/appLogin")
    @SaIgnore
    public ResponseResult<RegisterUserVo> appLogin(@RequestBody AppLoginDto dto){
        //校验手机号 是否存在库
        UserData userData = userDataMapper.queryByMobile(dto.getMobile());
        if (userData == null){
            return ResponseResult.error(ErrorCodeEnum.INVALID_TENANT_CODE , "用户不存在");
        }
        // TODO 验证用户在crm侧完成状态 暂定？
        if (userData.getAuditFlag() != 1){
            return ResponseResult.error(ErrorCodeEnum.INVALID_TENANT_CODE , "用户未审核通过");
        }
        if ("prod".equals(active)) {
            if (map.containsKey(dto.getMobile())) {
                if (!dto.getCode().equals(map.get(dto.getMobile()))) {
                    return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "验证码错误");
                }
            } else {
                // 验证码校验
                RBucket<String> bucket = redissonClient.getBucket(YZM_PREFIX + dto.getMobile());
                String msgId = bucket.get();
                if (msgId == null) {
                    return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "验证码已过期，请重新获取");
                }
                SendSmsVo vo = smsService.verifyCode(msgId, dto.getCode());
                if (!vo.getSuccessFlag()) {
                    return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "验证码错误");
                }
            }
        }
        CreateTokenDto createTokenDto = new CreateTokenDto();
        createTokenDto.setAccount(userData.getMobile());
        createTokenDto.setUserId(userData.getId());
        UserTokenUtil.createToken(createTokenDto);
        String tokenValue = createTokenDto.getToken();

        RegisterUserVo userVo = new RegisterUserVo();
        userVo.setToken(tokenValue);
        userVo.setUserId(userData.getId());
        userVo.setChannelId(userData.getChannelId());
        UserCreditData userCreditData = userCreditDataMapper.queryByUserId(userData.getId());
        if (userCreditData != null){
            userVo.setCreditAmount(userCreditData.getCreditAmount());
            if (userCreditData.getStatusFlag() == 3){
                userVo.setCreditAmount(BigDecimal.ZERO);
            }
        }
        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userData.getId(), 1, 0);
        if (userLoanApply != null && (userLoanApply.getAuditsStatus() == 1  || userLoanApply.getAuditsStatus() == 4)){
            userVo.setIsCredit(0);
        }else {
            userVo.setIsCredit(1);
        }
        userVo.setMobile(dto.getMobile().substring(0, 3) + "****" + dto.getMobile().substring(7));
        UserDetail detail = MyModelUtil.copyTo(dto, UserDetail.class);
        detail.setUserId(userData.getId());
        detail.setClientIp(IpUtil.getRemoteIpAddress(ContextUtil.getHttpRequest()));
        UpdateWrapper<UserDetail> wrapper = new UpdateWrapper();
        wrapper.eq("user_id",userData.getId());
        userDetailMapper.update(detail , wrapper);
        return ResponseResult.success(userVo);
    }


    @Operation(summary = "用户流程测查询  查询用户跳转页面")
    @PostMapping("/processState")
    public ResponseResult<ProcessStateVo> processState(){
        ProcessStateVo vo = new ProcessStateVo();
        Long userId = UserTokenUtil.getUserId();
        Integer productId = UserTokenUtil.getProductId();
        if(productId == null){
//            UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, 1, 0);//查询的是线上的授信申请
            productId = 0;
        }

        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userId, 1, productId);
        UserDetail detail = userDetailMapper.queryByUserId(userId);
        if(detail == null){
            return ResponseResult.success(vo);
        }
        if (userLoanApply == null || userLoanApply.getAuditsStatus() != 1){//审核状态不通过
            if (detail.getOcrResult() == 0){
                vo.setFlow(1);
            }else if (detail.getFaceResult() == 0 ){
                vo.setFlow(2);
            }else if (detail.getFormFlag() == 0 ){
                vo.setFlow(3);
            }else if (userLoanApply.getAuditsStatus() == 0){
                vo.setFlow(4);
            }else if (userLoanApply.getAuditsStatus() == 2){
                vo.setFlow(5);
            }
        }else {
            UserCreditResult result = userCreditResultMapper.selectByUserId(userId);
            if (result == null) {
                vo.setFlow(3);
            } else {
                if (result.getCreditStatus().equals(CreditStatusConstant.CREDITING)) {
                    vo.setFlow(4);
                } else if (result.getCreditStatus().equals(CreditStatusConstant.CREDIT_FAILURE)) {
                    vo.setFlow(5);
                } else {
                    DisburseData data = disburseDataMapper.selectByUserId(userId);
                    if (data == null ){
                        vo.setFlow(7);
                    }else {
                        if (data.getCreditStatus() != 100 && data.getCreditStatus() != 300){
                            vo.setFlow(7);
                        }else {
                            vo.setFlow(8);
                        }
                    }
                }
            }
        }
        return ResponseResult.success(vo);
    }

    @Operation(summary = "查询用户授信状态")
    @PostMapping("/queryH5CreditStatus")
    public ResponseResult<RegisterUserVo> queryCreditStatus(){
        RegisterUserVo userVo = new RegisterUserVo();
        Long userId = UserTokenUtil.getUserId();
        UserData userData = userDataMapper.selectById(userId);
        userVo.setUserId(userData.getId());
        userVo.setChannelId(userData.getChannelId());
        UserCreditData userCreditData = userCreditDataMapper.queryByUserId(userData.getId());
        if (userCreditData != null){
            userVo.setCreditAmount(userCreditData.getCreditAmount());
        }
        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userData.getId(), 1, 0);
        if (userLoanApply != null && (userLoanApply.getAuditsStatus() == 1  || userLoanApply.getAuditsStatus() == 4)){
            userVo.setIsCredit(0);
        }else {
            userVo.setIsCredit(1);
        }
        return ResponseResult.success(userVo);
    }


}
