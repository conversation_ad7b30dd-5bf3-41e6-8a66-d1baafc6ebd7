package com.rongchen.byh.app.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName sale_order_data
 */
@TableName(value ="sale_order_data")
@Data
public class SaleOrderData {
    /**
     * 自增id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 支用表id
     */
    private Long disburseId;

    /**
     * 赊销单号
     */
    private String saleNo;

    /**
     * 赊销金额
     */
    private BigDecimal saleOrderAmt;

    /**
     * 赊销订单类型  DYRD:融担 ZKBBL:保理  XNQY:虚拟权益
     */
    private String saleModel;

    /**
     * 供应商编码
     */
    private String saleChannel;

    /**
     * 
     */
    private String saleOrderNo;

    /**
     * 赊销利率
     */
    private String saleOrderRate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}