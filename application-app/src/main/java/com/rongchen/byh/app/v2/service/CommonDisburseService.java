package com.rongchen.byh.app.v2.service;

import com.rongchen.byh.app.dto.app.FaceVerifyDto;
import com.rongchen.byh.app.entity.DisburseData;
import com.rongchen.byh.app.v2.dto.capital.bindcard.*;
import com.rongchen.byh.app.v2.dto.capital.loan.CommonLoanApplyDto;
import com.rongchen.byh.app.v2.dto.capital.loan.CommonTrialPaymentDto;
import com.rongchen.byh.app.v2.entity.UserCapitalCreditRecord;
import com.rongchen.byh.app.v2.vo.capital.loan.CommonLoanApplyVo;
import com.rongchen.byh.common.api.zifang.vo.*;
import com.rongchen.byh.common.api.zifang.vo.contraact.ContractList;
import com.rongchen.byh.common.core.object.ResponseResult;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 支用业务
 * @date 2025/4/25 14:54:26
 */
public interface CommonDisburseService {

    /**
     * 获取协议列表
     * @param commonAgreementDto
     * @return
     */
    ResponseResult<List<ContractList>> queryAgreement(CommonAgreementDto commonAgreementDto);

    /**
     * 借款通道
     * @return
     */
    ResponseResult<LoanElementVo> loanElement(CommonLoanElementDto dto);

    /**
     * 获取验证码
     * @param dto
     * @return
     */
    ResponseResult<BindBankSmsVo> bindSendCode(CommonBindCardSmsDto dto);

    /**
     * 绑定银行卡
     * @param dto
     * @return
     */
    ResponseResult<VerifyBindBankSmsVo> verifyBindSend(CommonVerifyBindBankSmsDto dto);


    /**
     * 绑卡结果查询
     * @param dto
     * @return
     */
    ResponseResult<QueryBindBankResultVo> bindBankResult(CommonQueryBindBankResultDto dto);

    /**
     * 本息借款
     * @param dto
     * @return
     */
    ResponseResult<CommonLoanApplyVo> loanApply(CommonLoanApplyDto dto);

    /**
     * 借款试算
     * @param dto
     * @return
     */
    ResponseResult<RepayPlanCalcVo> trialPayment(CommonTrialPaymentDto dto);

    /**
     * 二次过人脸
     * @param verifyDto
     * @return
     */
    ResponseResult<Void> submitFace(FaceVerifyDto verifyDto);

    /**
     * 获取支用状态
     * @param disburseData
     * @return
     */
    ResponseResult<Void> loanStatusQuery(DisburseData disburseData);

    /**
     * 获取授信状态
     * @param record
     * @return
     */
    ResponseResult<Void> creditStatusQuery(UserCapitalCreditRecord record);
}
