package com.rongchen.byh.app.v2.service.impl;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.rongchen.byh.app.constant.LoanType;
import com.rongchen.byh.app.constant.SourceMode;
import com.rongchen.byh.app.dao.*;
import com.rongchen.byh.app.dto.CreateTokenDto;
import com.rongchen.byh.app.dto.YzmCodeDto;
import com.rongchen.byh.app.dto.app.AppLoginDto;
import com.rongchen.byh.app.entity.*;
import com.rongchen.byh.app.exceptions.BusinessException;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.app.v2.async.AsyncMethod;
import com.rongchen.byh.app.v2.common.constants.CreditStatusConstant;
import com.rongchen.byh.app.v2.dao.ProductDataMapper;
import com.rongchen.byh.app.v2.dao.UserCreditDataHistoryMapper;
import com.rongchen.byh.app.v2.dao.UserCreditResultMapper;
import com.rongchen.byh.app.v2.dao.UserLoanApplyHistoryMapper;
import com.rongchen.byh.app.v2.dto.LoginH5Dto;
import com.rongchen.byh.app.v2.entity.ProductData;
import com.rongchen.byh.app.v2.entity.UserCreditDataHistory;
import com.rongchen.byh.app.v2.entity.UserCreditResult;
import com.rongchen.byh.app.v2.entity.UserLoanApplyHistory;
import com.rongchen.byh.app.v2.handle.product.ProductHandleCenter;
import com.rongchen.byh.app.v2.handle.product.ProductHandleFactory;
import com.rongchen.byh.app.v2.service.ILoginV2Service;
import com.rongchen.byh.app.v2.service.IUserProgressService;
import com.rongchen.byh.app.vo.RegOrLoginVo;
import com.rongchen.byh.app.vo.app.RegisterUserVo;
import com.rongchen.byh.common.api.sms.service.SmsService;
import com.rongchen.byh.common.api.sms.vo.SendSmsVo;
import com.rongchen.byh.common.core.constant.ErrorCodeEnum;
import com.rongchen.byh.common.core.object.ResponseResult;
import com.rongchen.byh.common.core.util.ContextUtil;
import com.rongchen.byh.common.core.util.IpUtil;
import com.rongchen.byh.common.core.util.MyModelUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;


@Service
@Slf4j
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class LoginV2ServiceImpl implements ILoginV2Service {

    private final RedissonClient redissonClient;
    private final SmsService smsService;
    private final UserDataMapper userDataMapper;
    private  final StaffDataMapper staffDataMapper;
    private final UserLoanApplyMapper userLoanApplyMapper;
    private final ChannelDataMapper channelDataMapper;
    private final UserCreditDataMapper userCreditDataMapper;
    private final UserDetailMapper userDetailMapper;
    private final ProductDataMapper productDataMapper;

    private final UserCreditDataHistoryMapper userCreditDataHistoryMapper;
    private final UserLoanApplyHistoryMapper userLoanApplyHistoryMapper;
    private final DisburseDataMapper disburseDataMapper;
    @Autowired
    private IUserProgressService userProgressService;
    @Resource
    private RedisTemplate<String,Object> redisTemplate;
    @Autowired
    private AsyncMethod asyncMethod;
    @Resource
    private StaffAuditRecordMapper staffAuditRecordMapper;
    @Resource
    private UserCreditResultMapper userCreditResultMapper;
    @Value("${spring.profiles.active}")
    private String active;


    private static final String MOBILE_PREFIX = "mb:";
    private static final String IP_PREFIX = "ip:";
    private static final String YZM_PREFIX = "yzm:";

    private static final Map<String,String> map = new HashMap<>();

    static {
        map.put("18800000000","133133");
    }

    /**
     * h5 登录
     * @param loginH5Dto
     * @return
     */
    /**
     * h5 登录
     * @param loginH5Dto
     * @return
     */
    @Override
    public ResponseResult<RegOrLoginVo> loginByH5(LoginH5Dto loginH5Dto) {
        log.info("用户h5注册或登录请求参数：{}", loginH5Dto);

        if (!Validator.isMobile(loginH5Dto.getMobile())) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "手机号格式错误");
        }
        if ("prod".equals(active)) {
            RBucket<String> bucket = redissonClient.getBucket(YZM_PREFIX + loginH5Dto.getMobile());
            String msgId = bucket.get();
            if (msgId == null) {
                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "验证码已过期，请重新获取");
            }
            SendSmsVo vo = smsService.verifyCode(msgId, loginH5Dto.getCode());
            if (!vo.getSuccessFlag()) {
                return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "验证码错误");
            }
        }

        ChannelData channelData =  channelDataMapper.selectBySecret(loginH5Dto.getChannel());
        if (channelData == null) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "渠道不存在");
        }
        if (channelData.getStatus() != 1) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "渠道已禁用");
        }

        ProductData productData = productDataMapper.selectOne(new LambdaQueryWrapper<ProductData>().eq(ProductData::getCode, loginH5Dto.getProductCode()));
        if (ObjectUtil.isEmpty(productData)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "产品不存在");
        }

        // 用户注册
        UserData userData = userDataMapper.selectOne(new LambdaQueryWrapper<UserData>().eq(UserData::getMobile, loginH5Dto.getMobile()));
        if (ObjectUtil.isEmpty(userData)) {
            userData = createUser(loginH5Dto.getMobile(), channelData,productData.getId());
        } else {
            // 不同产品登录检测覆盖流程
            if (!coverUserInfo(userData, productData.getId())) {
//                return ResponseResult.error(ErrorCodeEnum.INVALID_USER_STATUS, "其他产品存在正在进行中的支出记录！");
                throw new BusinessException(ErrorCodeEnum.INVALID_USER_STATUS, "您已有申请中的额度！");
            }
        }

        ProductHandleFactory productHandle = ProductHandleCenter.getProductHandle(productData.getBeanName());
        ResponseResult<RegOrLoginVo> result = productHandle.loginByH5(loginH5Dto, userData);
        if (!result.isSuccess()) {
            return result;
        }

        // 生成token
        Long userId = userData.getId();
        CreateTokenDto createTokenDto = new CreateTokenDto();
        createTokenDto.setUserId(userId);
        createTokenDto.setAccount(loginH5Dto.getMobile());
        createTokenDto.setProductId(productData.getId());
        UserTokenUtil.createToken(createTokenDto);

        RegOrLoginVo regOrLoginVo = new RegOrLoginVo();
        regOrLoginVo.setToken(createTokenDto.getToken());
        regOrLoginVo.setUserId(userId);
        regOrLoginVo.setApplyStatus(result.getData().getApplyStatus()==null?0:result.getData().getApplyStatus());
        regOrLoginVo.setChannelId(channelData.getId());
        log.info("用户h5注册或登录响应参数：{}，请求参数：{}", regOrLoginVo, loginH5Dto);
        return ResponseResult.success(regOrLoginVo);
    }

    @Override
    public ResponseResult<Void> checkInviteCode(LoginH5Dto dto) {
        String inviteCode = dto.getInviteCode();
        StaffData staffData = staffDataMapper.selectByInviteCode(inviteCode);
        if (ObjectUtil.isEmpty(staffData)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"邀请码错误");
        }
        if (staffData.getInviteFlag() == 1) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED,"邀请码已失效");
        }
        return ResponseResult.success();
    }

    /**
     * app登录
     * @param dto
     * @return
     */
    @Override
    public ResponseResult<RegisterUserVo> loginByApp(AppLoginDto dto) {
        //校验手机号 是否存在库
        UserData userData = userDataMapper.queryByMobile(dto.getMobile());
        if (userData == null){
            return ResponseResult.error(ErrorCodeEnum.INVALID_TENANT_CODE , "用户不存在");
        }
        Integer sourceMode = userData.getSourceMode();
        Long userDataId = userData.getId();
        //对三种老模式进行判断，如果初审审核状态未通过则不允许用户登录
        if ( (sourceMode == SourceMode.ONLINE || sourceMode == SourceMode.OFFLINE || sourceMode == SourceMode.AIR) && userData.getAuditFlag() != 1){
            return ResponseResult.error(ErrorCodeEnum.INVALID_TENANT_CODE , "用户未审核通过");
        }
        if ("prod".equals(active)) {
            if (map.containsKey(dto.getMobile())) {
                if (!dto.getCode().equals(map.get(dto.getMobile()))) {
                    return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "验证码错误");
                }
            } else {
                // 验证码校验
                RBucket<String> bucket = redissonClient.getBucket(YZM_PREFIX + dto.getMobile());
                String msgId = bucket.get();
                if (msgId == null) {
                    return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "验证码已过期，请重新获取");
                }
                SendSmsVo vo = smsService.verifyCode(msgId, dto.getCode());
                if (!vo.getSuccessFlag()) {
                    return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "验证码错误");
                }
            }
        }
        CreateTokenDto createTokenDto = new CreateTokenDto();
        createTokenDto.setAccount(userData.getMobile());
        createTokenDto.setUserId(userData.getId());
        createTokenDto.setProductId(sourceMode);

        UserTokenUtil.createToken(createTokenDto);
        String tokenValue = createTokenDto.getToken();
        ProductData productData = productDataMapper.selectById(sourceMode);
        RegisterUserVo userVo = new RegisterUserVo();
        userVo.setProductCode(productData.getCode());
        userVo.setToken(tokenValue);
        userVo.setUserId(userData.getId());
        userVo.setChannelId(userData.getChannelId());
        UserCreditData userCreditData = userCreditDataMapper.queryByUserId(userData.getId());
        if (userCreditData != null){
            userVo.setCreditAmount(userCreditData.getCreditAmount());
        }
        // 授信申请 线上   不等于空 & 1审核通过 4转人工
        //此处用于前端判断在app首页跳转授信页面还是支用绑卡页面
//        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userData.getId(), 1, 0);
        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userData.getId(), 1, sourceMode);
        if (userLoanApply != null && (userLoanApply.getAuditsStatus() == 1  || userLoanApply.getAuditsStatus() == 4)){
             // 已授信
            // 判断是否资方授信通过
            UserCreditResult result = userCreditResultMapper.selectByUserId(userData.getId());
            if (result == null) {
                userVo.setIsCredit(1);
            } else {
                if (result.getCreditStatus().equals(CreditStatusConstant.CREDIT_SUCCESS)) {
                    userVo.setIsCredit(0);
                } else {
                    userVo.setIsCredit(1);
                }
            }
        }else {
            userVo.setIsCredit(1); // 未授信
        }
        userVo.setMobile(dto.getMobile().substring(0, 3) + "****" + dto.getMobile().substring(7));
        UserDetail detail = MyModelUtil.copyTo(dto, UserDetail.class);
        detail.setUserId(userDataId);
        detail.setClientIp(IpUtil.getRemoteIpAddress(ContextUtil.getHttpRequest()));
        UserDetail userDetail = userDetailMapper.queryByUserId(userDataId);
        if(userDetail != null){
            UpdateWrapper<UserDetail> wrapper = new UpdateWrapper();
            wrapper.eq("user_id",userDataId);
            userDetailMapper.update(detail , wrapper);
        }else{
            userDetailMapper.insert(detail);
        }
        return ResponseResult.success(userVo);
    }

    private UserData createUser(String mobile, ChannelData channelData,Integer productId) {
        UserData user = new UserData();
        user.setMobile(mobile);
        user.setStatusFlag(1);
        user.setAuditFlag(0);
        user.setMobileMd(DigestUtil.md5Hex(mobile, StandardCharsets.UTF_8.name()));
        user.setChannelId(channelData.getId());
        user.setCreateTime(new Date());
        user.setIp(IpUtil.getRemoteIpAddress(ContextUtil.getHttpRequest()));
        user.setSourceMode(productId);
        userDataMapper.insert(user);
        return user;
    }

    @Override
    public ResponseResult<Void> sendCode(YzmCodeDto yzmCodeDto) {
        String mobile = yzmCodeDto.getMobile();
        if (!Validator.isMobile(mobile)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "手机号格式错误");
        }
        HttpServletRequest httpRequest = ContextUtil.getHttpRequest();
        String ipAddress = IpUtil.getRemoteIpAddress(httpRequest);
        // 一个手机号2分钟3条
        if (!checkMobile(mobile)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "短信发送太频繁，请稍后再试");
        }
        // 一个IP一天20条
        if (!checkIp(ipAddress)) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "短信发送太频繁，请稍后再试");
        }
        // 发送短信
        SendSmsVo vo = smsService.sendSms(mobile);
        if (!vo.getSuccessFlag()) {
            return ResponseResult.error(ErrorCodeEnum.DATA_VALIDATED_FAILED, "短信发送失败，请稍后再试");
        }
        String msgId = vo.getMsgId();
        // 缓存验证码
        RBucket<String> bucket = redissonClient.getBucket(YZM_PREFIX + mobile);
        bucket.set(msgId + "", 2, TimeUnit.MINUTES);

        return ResponseResult.success();
    }


    private boolean checkIp(String ipAddress) {
        RBucket<Integer> bucket = redissonClient.getBucket(IP_PREFIX + ipAddress);
        Integer count = bucket.get();
        if (count == null) {
            bucket.set(1);
            bucket.expire(1, TimeUnit.DAYS);
            return true;
        }
        if (count >= 20) {
            return false;
        }
        bucket.set(count + 1, bucket.remainTimeToLive() / 1000, TimeUnit.SECONDS);
        return true;
    }

    private boolean checkMobile(String mobile) {
        RBucket<Integer> bucket = redissonClient.getBucket(MOBILE_PREFIX + mobile);
        Integer count = bucket.get();
        if (count == null) {
            bucket.set(1);
            bucket.expire(60 * 2, TimeUnit.SECONDS);
            return true;
        }
        if (count >= 3) {
            return false;
        }
        bucket.set(count + 1, bucket.remainTimeToLive() / 1000, TimeUnit.SECONDS);
        return true;
    }


    /**
     * 不同产品登录检测覆盖流程
     *
     * @param userData
     * @param productId
     */
    private boolean coverUserInfo(UserData userData, Integer productId) {
        Integer oldSourceMode = userData.getSourceMode();
        // 判断模式是否变更，不同则切换模式覆盖前流程
        if (!Objects.equals(oldSourceMode, productId)) {
            // 查询是否有授信额度
            UserCreditData userCreditData = userCreditDataMapper.queryByUserId(userData.getId());
            if (ObjectUtil.isNotEmpty(userCreditData)) {
                // 老用户 有支出且支出中 跳过覆盖
                LambdaQueryWrapper<DisburseData> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(DisburseData::getUserId, userData.getId());
                queryWrapper.in(DisburseData::getCreditStatus, Arrays.asList(100, 300, 500));
                boolean exists = disburseDataMapper.exists(queryWrapper);
                if (exists) {
                    return false;
                }
                //保留历史数据
                UserCreditDataHistory userCreditDataHistory = MyModelUtil.copyTo(userCreditData, UserCreditDataHistory.class);
                userCreditDataHistory.setId(null);
                userCreditDataHistoryMapper.insert(userCreditDataHistory);
                // 逻辑硬删除

                userCreditDataMapper.deleteById(userCreditData.getId());
                log.info("用户：{}信息覆盖，删除旧userCreditData：{}", userData.getId(), userCreditData);
            }
            // todo 覆盖状态需确定
            userData.setSourceMode(productId);
            userData.setAuditFlag(0);
            userData.setAuditStatus(1);
            userDataMapper.updateById(userData);
            UserDetail userDetail = userDetailMapper.queryByUserId(userData.getId());
            if (ObjectUtil.isNotEmpty(userDetail)){
                userDetail.setFaceResult(0);
                userDetail.setFormFlag(0);
                userDetail.setOcrResult(0);
                userDetailMapper.updateById(userDetail);
            }
            UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(userData.getId(), LoanType.CHECK, oldSourceMode);
            if (ObjectUtil.isNotEmpty(userLoanApply)) {
                //查询一下是否有授信申请，如果有也一起删掉并存储
                UserLoanApply loanApply = userLoanApplyMapper.selectByUserIdAndType(userData.getId(), LoanType.LOAN, oldSourceMode);
                if(loanApply!=null){
                    UserLoanApplyHistory loanHistory = MyModelUtil.copyTo(loanApply, UserLoanApplyHistory.class);
                    loanHistory.setId(null);
                    userLoanApplyHistoryMapper.insert(loanHistory);
                    userLoanApplyMapper.deleteById(loanApply.getId());
                }
                //he
                //保留历史数据
                UserLoanApplyHistory userLoanApplyHistory = MyModelUtil.copyTo(userLoanApply, UserLoanApplyHistory.class);
                userLoanApplyHistory.setId(null);
                userLoanApplyHistoryMapper.insert(userLoanApplyHistory);
                //逻辑硬删除
                userLoanApplyMapper.deleteById(userLoanApply.getId());
                log.info("用户：{}信息覆盖，删除userLoanApply：{}", userData.getId(), userLoanApply);
            }
            log.info("用户：{}旧产品用户信息覆盖完成", userData.getId());
            //异步删除user_progress表中，该用户的数据
            asyncMethod.delUserPrograss(userData.getId());
            //如果上一个模式是线下相关模式，则需要查看该用户是否有销售订单，如果有则删除
            if(oldSourceMode.equals(SourceMode.OFFLINE) || oldSourceMode.equals(SourceMode.OFFLINE_ONLY_REGISTER)){
                //判断该用户是否含有销售复核记录，如果有则删除
                String mobile = userData.getMobile();
                Integer recordId = userDataMapper.selectSaleRecordByMobile(mobile);
                if(recordId!=null){
                    staffAuditRecordMapper.deleteById(recordId);
                }
            }
        }
        return true;
    }


}
