package com.rongchen.byh.app.v2.controller.app;

import com.rongchen.byh.app.service.HrzxCreditLogService;
import com.rongchen.byh.app.utils.UserTokenUtil;
import com.rongchen.byh.app.v2.dto.FaceDecideDto;
import com.rongchen.byh.app.v2.service.ICapitalCreditService;
import com.rongchen.byh.app.v2.vo.CapitalRuleVo;
import com.rongchen.byh.app.v2.vo.FaceDecideVo;
import com.rongchen.byh.common.core.object.ResponseResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 通用api接口
 * @date 2025/4/27 17:58:54
 */
@Tag(name = "通用api接口")
@RestController
@RequestMapping("/v2/common")
public class CommonApiController {
    @Resource
    private ICapitalCreditService capitalCreditService;
    @Resource
    private HrzxCreditLogService hrzxCreditLogService;

    @Operation(summary = "资方路由")
    @PostMapping("/capitalRule")
    public ResponseResult<CapitalRuleVo> capitalRule(){
        return capitalCreditService.capitalRule(UserTokenUtil.getUserId());
    }

    @Operation(summary = "支用部分过人脸判断")
    @PostMapping("/faceDecide")
    public ResponseResult<FaceDecideVo> faceDecide(@RequestBody @Validated FaceDecideDto dto){
        return hrzxCreditLogService.faceDecide(dto);
    }
}
