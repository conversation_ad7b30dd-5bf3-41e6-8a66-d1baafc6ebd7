package com.rongchen.byh.app.v2.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.rongchen.byh.app.v2.entity.ApiPushRecord;
import com.rongchen.byh.app.v2.vo.ApiPushRecordVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ApiPushRecordMapper extends BaseMapper<ApiPushRecord> {
    ApiPushRecord selectByOrderNo(String orderNo);

    /**
     * 获取昨天23点的放款计划变更列表
     * @return
     */
    List<ApiPushRecordVo> selectScheduleChangeList();

    List<ApiPushRecordVo> selectNoNoticeList(ApiPushRecord apiPushRecord);

    List<ApiPushRecordVo> selectNoDisburseNotifyList(Integer num);

    List<ApiPushRecordVo> selectBillOverDueList(Integer num);

    List<ApiPushRecordVo> selectBillSettleList(Integer num);
}
