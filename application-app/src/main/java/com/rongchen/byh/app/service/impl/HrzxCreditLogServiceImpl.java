package com.rongchen.byh.app.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.rongchen.byh.app.dao.HrzxCreditLogMapper;
import com.rongchen.byh.app.service.HrzxCreditLogService;
import com.rongchen.byh.app.v2.dto.FaceDecideDto;
import com.rongchen.byh.app.v2.vo.FaceDecideVo;
import com.rongchen.byh.common.core.object.ResponseResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2025/4/28 10:37:23
 */
@Service
public class HrzxCreditLogServiceImpl implements HrzxCreditLogService {
    @Resource
    private HrzxCreditLogMapper hrzxCreditLogMapper;

    @Override
    public ResponseResult<FaceDecideVo> faceDecide(FaceDecideDto dto) {
        FaceDecideVo vo = new FaceDecideVo();
        vo.setFaceFlag(true);
        if (ObjectUtil.isEmpty(dto.getIsApi()) || !dto.getIsApi()) {
            vo.setFaceFlag(false);
            return ResponseResult.success(vo);
        }
        if (ObjectUtil.isEmpty(dto.getOrderNo())) {
            vo.setFaceFlag(false);
            return ResponseResult.success(vo);
        }
        int count = hrzxCreditLogMapper.faceDecide(dto);
        if (count > 0) {
            vo.setFaceFlag(false);
        }
        return ResponseResult.success(vo);
    }
}
