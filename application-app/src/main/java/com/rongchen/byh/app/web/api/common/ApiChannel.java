package com.rongchen.byh.app.web.api.common;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 外部渠道枚举
 */
@Getter
@RequiredArgsConstructor
public enum ApiChannel {

    RONG360("rong360", "融360渠道"),
    JUYOU("juyou", "聚有渠道"),
    YOUJIE("youjie", "柚借渠道");
    // 可继续添加其他渠道

    private static final Map<String, ApiChannel> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(ApiChannel::getCode, Function.identity()));
    private final String code;
    private final String description;

    /**
     * 根据渠道代码获取枚举实例 (忽略大小写)
     * 
     * @param code 渠道代码字符串
     * @return 对应的 ApiChannel 枚举，如果未找到则返回 null
     */
    public static ApiChannel fromCode(String code) {
        return CODE_MAP.get(code != null ? code.toLowerCase() : null);
    }
}