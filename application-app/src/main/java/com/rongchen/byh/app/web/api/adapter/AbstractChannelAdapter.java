package com.rongchen.byh.app.web.api.adapter;

import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.web.api.common.ApiOperation;
import javax.servlet.http.HttpServletRequest;

import com.rongchen.byh.common.core.object.ResponseResult;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 渠道适配器抽象基类 (模板方法模式)
 * <p>
 * 定义了处理外部渠道 API 请求的标准流程：前置处理 -> 核心业务逻辑 -> 后置处理。
 * 子类需要通过继承此类，并实现以下抽象方法或覆盖默认方法：
 * <ul>
 * <li>{@link #preProcess(String, ApiOperation, HttpServletRequest)}:
 * 实现特定渠道的请求解析、验签、解密等前置逻辑。</li>
 * <li>核心业务逻辑方法 (例如 {@link #creditApply(String, JSONObject)}):
 * 子类覆盖此方法以实现具体业务操作。</li>
 * <li>{@link #postProcess(String, ApiOperation, JSONObject)}:
 * 实现特定渠道的响应封装、签名、加密等后置逻辑。</li>
 * <li>(可选) {@link #buildErrorResponse(String, String, Exception)}:
 * 实现特定渠道的错误响应格式。</li>
 * <li>(可选) {@link #supports(String, ApiOperation)}: 如果需要比仅匹配渠道代码更复杂的支持逻辑。</li>
 * </ul>
 * 控制器应调用此类提供的统一入口
 * {@link #handleRequest(String, ApiOperation, HttpServletRequest)}。
 */
@Getter
@Slf4j
public abstract class AbstractChannelAdapter {

    /**
     * -- GETTER --
     * 获取此适配器支持的渠道代码 (小写)
     *
     * @return 渠道代码 (例如 "rong360")
     */
    protected final String supportedChannelCode;

    /**
     * 构造函数。
     * 初始化适配器并设置其支持的渠道代码。
     *
     * @param supportedChannelCode 子类适配器支持的渠道代码 (例如 "rong360")，将被转换为小写存储。
     * @throws IllegalArgumentException 如果渠道代码为 null 或空。
     */
    protected AbstractChannelAdapter(String supportedChannelCode) {
        if (supportedChannelCode == null || supportedChannelCode.trim().isEmpty()) {
            throw new IllegalArgumentException("支持的渠道代码不能为空");
        }
        this.supportedChannelCode = supportedChannelCode.toLowerCase();
        log.info("初始化适配器，渠道: {}", this.supportedChannelCode);
    }

    /**
     * 检查此适配器是否支持给定的渠道。
     * <p>
     * 基础实现仅通过比较渠道代码（忽略大小写）来判断。
     * 子类可以覆盖此方法以实现更复杂的逻辑，例如结合 {@code operation} 参数来判断是否支持特定操作。
     *
     * @param channel   待检查的渠道标识符 (来自请求路径)
     * @param operation 当前请求的操作类型 (基础实现未使用，保留供子类覆盖)
     * @return {@code true} 如果此适配器支持该渠道代码，否则返回 {@code false}
     */
    public boolean supports(String channel, ApiOperation operation) {
        boolean channelMatch = this.supportedChannelCode.equalsIgnoreCase(channel);
        if (!channelMatch) {
            log.trace("渠道不匹配，适配器 [{}]: 期望='{}', 实际='{}'",
                    this.getClass().getSimpleName(), this.supportedChannelCode, channel);
        }
        return channelMatch;
    }

    // --- 统一处理入口 --- //

    /**
     * 处理任何 API 请求的统一入口方法。
     * 调用此方法将触发标准的处理流程：preProcess -> executeCoreLogic -> postProcess。
     *
     * @param channel   渠道标识
     * @param operation 需要执行的操作类型
     * @param request   HTTP请求对象
     * @return 处理结果 (JSONObject)
     * @throws Exception 处理过程中可能发生的异常
     */
    public final JSONObject handleRequest(String channel, ApiOperation operation, HttpServletRequest request)
            throws Exception {
        // 直接调用核心处理模板
        return handleTemplate(channel, operation, request);
    }

    /**
     * 通用处理流程模板。
     * 定义了 预处理 -> 核心逻辑执行 -> 后处理 的标准步骤。
     *
     * @param channel   渠道标识
     * @param operation 操作类型
     * @param request   HTTP请求
     * @return 最终处理完成的响应 JSONObject
     * @throws Exception 在处理流程中发生的任何异常将向上抛出
     */
    private JSONObject handleTemplate(String channel, ApiOperation operation, HttpServletRequest request)
            throws Exception {
        JSONObject processedRequestData = null;
        JSONObject businessResult = null;
        JSONObject structuredSuccessResponse = null;
        JSONObject finalResponse = null;
        long startTime = System.currentTimeMillis();
        try {
            log.info("渠道[{}] -> 开始前置处理，操作: {}", channel, operation.getCode());
            processedRequestData = preProcess(channel, operation, request);
            log.info("渠道[{}] <- 完成前置处理，操作: {}", channel, operation.getCode());

            log.info("渠道[{}] -> 执行核心逻辑，操作: {}", channel, operation.getCode());
            businessResult = executeCoreLogic(channel, operation, processedRequestData);
            log.info("渠道[{}] <- 完成核心逻辑，操作: {}", channel, operation.getCode());

            log.info("渠道[{}] -> 开始构建成功响应，操作: {}", channel, operation.getCode());
            structuredSuccessResponse = buildSuccessResponse(channel, operation, businessResult);
            log.info("渠道[{}] <- 完成构建成功响应，操作: {}", channel, operation.getCode());

            log.info("渠道[{}] -> 开始后置处理，操作: {}", channel, operation.getCode());
            finalResponse = postProcess(channel, operation, structuredSuccessResponse);
            log.info("渠道[{}] <- 完成后置处理，操作: {}", channel, operation.getCode());

            return finalResponse;

        } catch (Exception e) {
            log.error(
                    "渠道[{}] 处理模板方法时出错，操作 [{}]: 前置处理数据={}, 业务结果={}, 结构化响应={}, 最终响应={}",
                    channel, operation.getCode(), processedRequestData, businessResult, structuredSuccessResponse,
                    finalResponse, e);
            throw e; // 重新抛出，由 BaseApiController 捕获
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            log.info("渠道[{}] 操作 [{}] 总执行耗时: {}ms", channel, operation.getCode(), duration);
        }
    }

    // --- 子类必须实现的方法 (preProcess, postProcess) --- //

    protected abstract JSONObject preProcess(String channel, ApiOperation operation, HttpServletRequest request)
            throws Exception;

    /**
     * 执行响应的后置处理 (例如签名、加密等)。
     * <p>
     * 子类必须实现此方法，将已结构化的成功响应进行最终处理，
     * 例如添加渠道要求的签名、时间戳，或者对响应进行加密。
     *
     * @param channel                   渠道标识
     * @param operation                 操作类型
     * @param structuredSuccessResponse 已经结构化的成功响应数据 (由
     *                                  {@link #buildSuccessResponse} 生成)
     * @return 最终处理后的响应数据，将直接返回给客户端
     * @throws Exception 处理过程中可能发生的异常
     */
    protected abstract JSONObject postProcess(String channel, ApiOperation operation,
                                              JSONObject structuredSuccessResponse)
            throws Exception;

    // --- Reverted executeCoreLogic --- //

    /**
     * 执行核心业务逻辑的分发方法。
     * <p>
     * 此方法根据 {@code operation} 参数，调用相应的具体核心业务逻辑方法（例如 {@link #creditApply}）。
     * 子类应覆盖它们支持的操作的核心业务逻辑方法。
     *
     * @param channel              渠道标识
     * @param operation            操作类型
     * @param processedRequestData 经过 {@link #preProcess} 处理后的请求数据
     * @return 核心业务逻辑的处理结果
     * @throws Exception 如果核心业务逻辑执行失败或操作不被支持
     */
    protected JSONObject executeCoreLogic(String channel, ApiOperation operation, JSONObject processedRequestData)
            throws Exception {
        log.info("渠道[{}] executeCoreLogic 分发操作 [{}]", channel, operation.getCode());
        switch (operation) {
            // === 授信 Credit ===
            case CREDIT_APPLY:
                return creditApply(channel, processedRequestData);
            case CREDIT_RESULT_QUERY:
                return creditQueryResult(channel, processedRequestData);
            case USER_ACCESS:
                return userAccess(channel, processedRequestData);
            case PROTOCOL_QUERY:
                return protocolQuery(channel, processedRequestData);
            case CREDIT_LIMIT_QUERY:
                return creditLimitQuery(channel, processedRequestData);
            // === 借款 Loan ===
            case LOAN_TRIAL:
                return loanTrial(channel, processedRequestData);
            case LOAN_APPLY:
                return loanApply(channel, processedRequestData);
            case LOAN_RESULT_QUERY:
                return loanResultQuery(channel, processedRequestData);
            // === 还款 Repay ===
            case REPAY_TRIAL:
                return repayTrial(channel, processedRequestData);
            case REPAY_APPLY:
                return repayApply(channel, processedRequestData);
            case REPAY_PLAN_QUERY:
                return repayPlanQuery(channel, processedRequestData);
            case REPAY_RESULT_QUERY:
                return repayResultQuery(channel, processedRequestData);
            // === 特殊接口 Special ===
            case GET_LOAN_H5_URL:
                return getLoanH5Url(channel, processedRequestData);
            case GET_REPAY_H5_URL:
                return getRepayH5Url(channel, processedRequestData);
            // === 银行卡 Card ===
            case CARD_SUPPORT_LIST:
                return cardSupportList(channel, processedRequestData);
            case CARD_BIND_SMS:
                return cardBindSms(channel, processedRequestData);
            case CARD_BIND_VALIDATE:
                return cardBindValidate(channel, processedRequestData);
            case CARD_BIND_QUERY:
                return cardBindQuery(channel, processedRequestData);
            case CARD_BIND_LIST:
                return cardBindList(channel, processedRequestData);
            case ORDER_STATUS:
                return orderStatus(channel, processedRequestData);
            default:
                // 对于未知的或回调类型的操作
                log.error("渠道[{}] 在 executeCoreLogic 中遇到未处理的操作 [{}].", channel, operation.getCode());
                throw unsupported(operation, "未知的操作类型");
        }
    }

    // --- 核心业务逻辑方法 (子类按需覆盖, 默认抛出异常) --- //

    /**
     * 抛出表示操作不支持的异常。
     *
     * @param operation 不支持的操作
     * @param reason    不支持的原因描述 (可选)
     * @return UnsupportedOperationException 实例
     */
    protected UnsupportedOperationException unsupported(ApiOperation operation, String... reason) {
        String reasonDesc = (reason != null && reason.length > 0 && reason[0] != null) ? reason[0] : "此适配器未实现该操作";
        String message = String.format("操作 [%s] (域 [%s]) 不被适配器 [%s] (渠道 [%s]) 支持。原因: %s",
                operation.getCode(), operation.getDomain(), this.getClass().getSimpleName(), this.supportedChannelCode,
                reasonDesc);
        log.warn(message);
        return new UnsupportedOperationException(message);
    }

    // === 授信 Credit ===
    /**
     * 处理用户准入检查的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的用户准入检查处理。
     * 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 用户准入检查的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    protected JSONObject userAccess(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.USER_ACCESS);
    }

    /**
     * 处理授信申请的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的授信申请处理。
     * 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 授信申请的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    protected JSONObject creditApply(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.CREDIT_APPLY);
    }

    /**
     * 处理授信结果查询的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的授信结果查询处理。
     * 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 授信结果查询的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    protected JSONObject creditQueryResult(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.CREDIT_RESULT_QUERY);
    }
    /**
     * 授信结果通知
     * <p>
     * 子类应覆盖此方法以实现具体的授信结果查询处理。
     * 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 授信结果查询的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    public ResponseResult<JSONObject> creditResultNotice(String channel, JSONObject processedRequestData)throws Exception{
        throw unsupported(ApiOperation.NOTIFY_CREDIT_RESULT);
    }



    /**
     * 处理协议查询的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的协议查询处理。
     * 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 协议查询的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    protected JSONObject protocolQuery(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.PROTOCOL_QUERY);
    }

    /**
     * 处理授信额度查询的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的授信额度查询处理。
     * 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 授信额度查询的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    protected JSONObject creditLimitQuery(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.CREDIT_LIMIT_QUERY);
    }

    // === 借款 Loan ===

    /**
     * 处理借款试算的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的借款试算处理。
     * 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 借款试算的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    protected JSONObject loanTrial(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.LOAN_TRIAL);
    }

    /**
     * 处理借款申请的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的借款申请处理。
     * 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 借款申请的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    protected JSONObject loanApply(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.LOAN_APPLY);
    }

    /**
     * 处理借款结果查询的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的借款结果查询处理。
     * 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 借款结果查询的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    protected JSONObject loanResultQuery(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.LOAN_RESULT_QUERY);
    }
    /**
     * 处理借款结果通知的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的借款结果查询处理。
     * 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 借款结果查询的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    public ResponseResult<Void> loanResultNotice(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.NOTIFY_LOAN_RESULT);
    }

    // === 还款 Repay ===

    /**
     * 处理还款试算的核心逻辑。
     * <p>
     * 子类应覆盖此方法。默认抛出 {@link UnsupportedOperationException}。
     * </p>
     * 
     * @param channel              渠道标识
     * @param processedRequestData 处理后的请求数据
     * @return 还款试算结果
     * @throws Exception 处理异常
     */
    protected JSONObject repayTrial(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.REPAY_TRIAL);
    }

    /**
     * 处理还款申请 (主动/预还款) 的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的还款申请处理。
     * 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 还款申请的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    protected JSONObject repayApply(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.REPAY_APPLY);
    }

    /**
     * 处理还款计划查询的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的还款计划查询处理。
     * 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 还款计划查询的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    protected JSONObject repayPlanQuery(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.REPAY_PLAN_QUERY);
    }

    /**
     * 处理还款计划通知的核心逻辑。
     * <p>
     * 子类应覆盖此方法。默认抛出 {@link UnsupportedOperationException}。
     * </p>
     *
     * @param channel              渠道标识
     * @param processedRequestData 处理后的请求数据
     * @return 还款计划通知结果
     * @throws Exception 处理异常
     */
    public ResponseResult<JSONObject> repayPlanNotice(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.NOTIFY_REPAY_PLAN);
    }


    /**
     * 处理订单状态通知的核心逻辑。
     * <p>
     * 子类应覆盖此方法。默认抛出 {@link UnsupportedOperationException}。
     * </p>
     *
     * @param channel              渠道标识
     * @param processedRequestData 处理后的请求数据
     * @return 订单状态通知结果
     * @throws Exception 处理异常
     */
    public ResponseResult<JSONObject> orderStatusNotice(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.NOTIFY_ORDER_STATUS);
    }

    /**
     * 处理还款结果查询的核心逻辑。
     * <p>
     * 子类应覆盖此方法以实现具体的还款结果查询处理。
     * 默认实现抛出 {@link UnsupportedOperationException}。
     *
     * @param channel              渠道标识
     * @param processedRequestData 经过 preProcess 处理后的请求数据
     * @return 还款结果查询的处理结果
     * @throws Exception 处理过程中可能发生的异常
     */
    protected JSONObject repayResultQuery(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.REPAY_RESULT_QUERY);
    }

    // === 特殊接口 Special ===

    /**
     * 处理获取借款 H5 页面 URL 的核心逻辑。
     * <p>
     * 子类应覆盖此方法。默认抛出 {@link UnsupportedOperationException}。
     * </p>
     *
     * @param channel              渠道标识
     * @param processedRequestData 处理后的请求数据
     * @return 获取借款 H5 URL 的处理结果
     * @throws Exception 处理异常
     */
    protected JSONObject getLoanH5Url(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.GET_LOAN_H5_URL);
    }

    /**
     * 处理获取还款 H5 页面 URL 的核心逻辑。
     * <p>
     * 子类应覆盖此方法。默认抛出 {@link UnsupportedOperationException}。
     * </p>
     *
     * @param channel              渠道标识
     * @param processedRequestData 处理后的请求数据
     * @return 获取还款 H5 URL 的处理结果
     * @throws Exception 处理异常
     */
    protected JSONObject getRepayH5Url(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.GET_REPAY_H5_URL);
    }

    // === 银行卡 Card ===

    /**
     * 处理支持银行卡列表查询的核心逻辑。
     * <p>
     * 子类应覆盖此方法。默认抛出 {@link UnsupportedOperationException}。
     * </p>
     *
     * @param channel              渠道标识
     * @param processedRequestData 处理后的请求数据
     * @return 查询结果
     * @throws Exception 处理异常
     */
    protected JSONObject cardSupportList(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.CARD_SUPPORT_LIST);
    }

    /**
     * 处理绑卡短信发送的核心逻辑。
     * <p>
     * 子类应覆盖此方法。默认抛出 {@link UnsupportedOperationException}。
     * </p>
     *
     * @param channel              渠道标识
     * @param processedRequestData 处理后的请求数据
     * @return 短信发送结果
     * @throws Exception 处理异常
     */
    protected JSONObject cardBindSms(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.CARD_BIND_SMS);
    }

    /**
     * 处理绑卡提交验证的核心逻辑。
     * <p>
     * 子类应覆盖此方法。默认抛出 {@link UnsupportedOperationException}。
     * </p>
     *
     * @param channel              渠道标识
     * @param processedRequestData 处理后的请求数据
     * @return 绑卡验证结果
     * @throws Exception 处理异常
     */
    protected JSONObject cardBindValidate(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.CARD_BIND_VALIDATE);
    }

    /**
     * 处理银行卡绑卡查询的核心逻辑。
     * <p>
     * 子类应覆盖此方法。默认抛出 {@link UnsupportedOperationException}。
     * </p>
     *
     * @param channel              渠道标识
     * @param processedRequestData 处理后的请求数据
     * @return 查询结果
     * @throws Exception 处理异常
     */
    protected JSONObject cardBindQuery(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.CARD_BIND_QUERY);
    }

    /**
     * 处理已绑卡列表查询的核心逻辑。
     * <p>
     * 子类应覆盖此方法。默认抛出 {@link UnsupportedOperationException}。
     * </p>
     *
     * @param channel              渠道标识
     * @param processedRequestData 处理后的请求数据
     * @return 列表结果
     * @throws Exception 处理异常
     */
    protected JSONObject cardBindList(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.CARD_BIND_LIST);
    }

    /**
     * 查询订单状态
     */
    protected JSONObject orderStatus(String channel, JSONObject processedRequestData) throws Exception {
        throw unsupported(ApiOperation.ORDER_STATUS);
    }

    // --- 错误响应构建方法 (子类按需覆盖, 保持不变) --- //

    /**
     * 构建一个通用的错误响应 JSONObject。
     * <p>
     * 子类可以覆盖此方法以实现特定渠道的错误响应格式，但通常不需要。
     *
     * @param errorCode    错误代码 (例如 "SYSTEM_ERROR")
     * @param errorMessage 错误描述信息
     * @param exception    触发错误的原始异常 (可以为 null)，用于记录日志
     * @return 包含 'responseCode' 和 'responseMsg' 的 JSONObject
     */
    public JSONObject buildErrorResponse(String errorCode, String errorMessage, Exception exception) {
        log.warn("渠道[{}] 正在构建通用错误响应: code={}, message={}, exception={}",
                this.supportedChannelCode, errorCode, errorMessage,
                (exception != null ? exception.getClass().getName() : "null"));
        JSONObject errorResult = new JSONObject();
        errorResult.put("responseCode", errorCode);
        String safeErrorMessage = errorMessage != null ? errorMessage : "未知错误";
        String escapedMsg = safeErrorMessage.replace("\"", "\\\"").replace("\n", "\\n").replace("\r", "\\r");
        errorResult.put("responseMsg", escapedMsg);
        return errorResult;
    }

    /**
     * 构建一个通用的成功响应 JSONObject。
     * <p>
     * 此方法负责将业务逻辑执行结果 (businessResult) 转换为符合特定渠道期望响应结构的 JSONObject。
     * 基础实现直接返回输入的业务结果，子类可以覆盖此方法以实现特定渠道的成功响应格式。
     * <p>
     * 这个方法的返回值随后将传递给 {@link #postProcess} 方法进行最终处理 (例如签名、加密)。
     *
     * @param channel        渠道标识
     * @param operation      操作类型
     * @param businessResult 核心业务逻辑的执行结果
     * @return 结构化的成功响应，准备被进一步处理 (签名、加密等)
     */
    protected JSONObject buildSuccessResponse(String channel, ApiOperation operation, JSONObject businessResult) {
        log.trace("渠道[{}] 正在使用默认成功响应构建器，操作: {}", channel, operation.getCode());
        JSONObject result = new JSONObject();
        result.put("responseCode", "0000");
        result.put("responseMsg", "成功");
        result.put("data", businessResult);
        return result; // 默认实现直接返回业务结果
    }
}