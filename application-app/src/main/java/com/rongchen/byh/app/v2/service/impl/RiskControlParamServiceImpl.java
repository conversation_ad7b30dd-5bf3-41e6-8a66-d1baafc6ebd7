package com.rongchen.byh.app.v2.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.rongchen.byh.app.constant.LoanType;
import com.rongchen.byh.app.dao.UserLoanApplyMapper;
import com.rongchen.byh.app.entity.UserDetail;
import com.rongchen.byh.app.entity.UserLoanApply;
import com.rongchen.byh.app.utils.OssUtil;
import com.rongchen.byh.app.v2.service.RiskControlParamService;
import com.rongchen.byh.common.api.bankCredit.dto.HrzxAuthDto;
import com.rongchen.byh.common.api.riskControl.dto.PreLoanAuditAppDto;
import com.rongchen.byh.common.api.riskControl.dto.PreLoanAuditAppRelationsDto;
import com.rongchen.byh.common.api.zifang.utils.CityCodeUtil;
import com.rongchen.byh.common.api.zifang.utils.IdCardUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 风控组装参数
 * @date 2025/4/27 14:28:01
 */
@Service
@Slf4j
public class RiskControlParamServiceImpl implements RiskControlParamService {

    @Resource
    private OssUtil ossUtil;

    @Resource
    private UserLoanApplyMapper userLoanApplyMapper;

    @Override
    public PreLoanAuditAppDto buildCommonParam(HrzxAuthDto hrzxAuthDto, UserDetail detail, String mobile) {
        // 调用app授信 获取授信结果
        List<PreLoanAuditAppRelationsDto> list = new ArrayList<>();
        PreLoanAuditAppRelationsDto relationsDto = new PreLoanAuditAppRelationsDto();
        relationsDto.setRelation_mobile(detail.getEmergencyMobileOne());
        relationsDto.setRelation_name(detail.getEmergencyNameOne());
        relationsDto.setRelation_type(Integer.valueOf(detail.getRelationshipOne()));
        list.add(relationsDto);
        PreLoanAuditAppRelationsDto relationsDto1 = new PreLoanAuditAppRelationsDto();
        relationsDto1.setRelation_mobile(detail.getEmergencyMobileTwo());
        relationsDto1.setRelation_name(detail.getEmergencyNameTwo());
        relationsDto1.setRelation_type(Integer.valueOf(detail.getRelationshipTwo()));
        list.add(relationsDto1);
        PreLoanAuditAppDto auditAppDto = new PreLoanAuditAppDto();
        auditAppDto.setIdcard_no(detail.getIdNumber());
        auditAppDto.setMobile(mobile);
        auditAppDto.setName(detail.getAppName());
        auditAppDto.setCredit_id(IdUtil.fastSimpleUUID());
        auditAppDto.setCredit_time(DateUtil.now());
        auditAppDto.setChannel("4");
        auditAppDto.setProduct_code(4);
        String[] split = detail.getValidDate().split("-");
        auditAppDto.setCert_start(split[0].replace(".", "-"));
        if ("长期".equals(split[1])) {
            split[1] = "2099.12.31";
        }
        auditAppDto.setCert_end(split[1].replace(".", "-"));
        auditAppDto.setNation_ocr(IdCardUtil.nationOcr(detail.getNation()));
        auditAppDto.setIdcard_address_ocr(detail.getAddress());
        auditAppDto.setEducation(IdCardUtil.educationSw(detail.getEducationLevel()));
        auditAppDto.setMarriage_state(IdCardUtil.marriageStateSw(detail.getMaritalStatus()));
        auditAppDto.setLive_address(detail.getCustAddressProvice() + detail.getCustAddressCity()
                + detail.getCustAddressCounty() + detail.getCustAddress());
        String resolution = IdCardUtil.addressResolution(detail.getAddress());
        if (resolution != null) {
            auditAppDto.setIdcard_city_code(resolution);
        } else {
            auditAppDto.setIdcard_city_code(CityCodeUtil.getCodeByName(detail.getCustAddressCity()));
        }
        auditAppDto.setLive_city_code(CityCodeUtil.getCodeByName(detail.getCustAddressCity()));
        auditAppDto.setIssued_org_ocr(detail.getAuthority());
        auditAppDto.setAnnual_income(Integer.valueOf(detail.getIncomeMonth()) * 12);
        auditAppDto.setIf_register("Y");
        auditAppDto.setIf_sign("Y");
        UserLoanApply userLoanApply = userLoanApplyMapper.selectByUserIdAndType(detail.getUserId(), LoanType.CHECK, 0);
        if (ObjectUtil.isNotEmpty(userLoanApply) && userLoanApply.getAuditsStatus() == 4) {
            auditAppDto.setIf_sign("N");
        }
        auditAppDto.setPhone_number("");
        auditAppDto.setMobile_startime("");
        auditAppDto.setWifi_sensitive("");
        auditAppDto.setAddress_book_num(0);
        auditAppDto.setAddress_book_num11(0);
        auditAppDto.setAddress_book_sensitive("");
        auditAppDto.setContact_operator("");
        auditAppDto.setOverdue_message_m60(0);
        JSONObject param = new JSONObject();
        param.put("house", IdCardUtil.houseSw(detail.getHouseStatus()));
        auditAppDto.setHouse_info(param);
        auditAppDto.setRelations(list);

        // Call the modified buildCreditInfo and get the JSONObject
        JSONObject socialCreditCodeResult = buildCreditInfo(hrzxAuthDto, detail, mobile);

        // Now create the companyInfo JSONObject using the result
        JSONObject companyInfo = new JSONObject();
        // Encode the entire result JSONObject for the 'social_credit_code' field (as it
        // was previously)
        companyInfo.put("social_credit_code", Base64.getEncoder().encodeToString(socialCreditCodeResult.toJSONString()
                .getBytes(StandardCharsets.UTF_8)));

        auditAppDto.setCompany_info(companyInfo);
        return auditAppDto;
    }

    private JSONObject buildCreditInfo(HrzxAuthDto hrzxAuthDto, UserDetail detail, String mobile) {
        JSONObject socialCreditCode = new JSONObject();
        socialCreditCode.put("requestNo", IdUtil.fastSimpleUUID());
        socialCreditCode.put("queryName", detail.getAppName());
        socialCreditCode.put("queryIdNoType", "01");
        socialCreditCode.put("queryIdNo", detail.getIdNumber());
        socialCreditCode.put("queryPhone", mobile);
        socialCreditCode.put("veriFaceTime", DateUtil.now());
        socialCreditCode.put("authTime", DateUtil.now());

        // --- 开始计时 ---
        long ossStartTime = System.currentTimeMillis();
        log.info("开始并行 OSS 操作，userId: {}, 手机号: {}", detail.getUserId(), mobile);
        log.info("身份证正面: {}", hrzxAuthDto.getFrontIdCardFile());
        log.info("身份证反面: {}", hrzxAuthDto.getBackIdCardFile());
        log.info("人脸: {}", hrzxAuthDto.getOtherAuthFile());
        log.info("ca电子签名授权书pdf文件: {}", hrzxAuthDto.getCaFile());
        log.info("委托担保申请书: {}", hrzxAuthDto.getOtherFile());
        // 开始并行 OSS 操作
        CompletableFuture<String> idCardFileFuture = ossUtil.mergeTwoImage(hrzxAuthDto.getFrontIdCardFile(),
                hrzxAuthDto.getBackIdCardFile(), mobile);
        CompletableFuture<String> otherAuthFileFuture = ossUtil.uploadOss(hrzxAuthDto.getOtherAuthFile());
        CompletableFuture<String> caFileFuture = ossUtil.uploadOss(hrzxAuthDto.getCaFile());
        CompletableFuture<String> otherFileFuture = ossUtil.pdfToZip(hrzxAuthDto.getOtherFile());

        // 等待所有操作完成
        CompletableFuture.allOf(idCardFileFuture, otherAuthFileFuture, caFileFuture, otherFileFuture).join();

        // --- 结束计时 ---
        long ossEndTime = System.currentTimeMillis();
        log.info("并行 OSS 操作完成，耗时: {} ms，userId: {}, 手机号: {}", (ossEndTime - ossStartTime), detail.getUserId(),
                mobile);

        // 组装 socialCreditCode 对象
        socialCreditCode.put("idCardFile", idCardFileFuture.join());
        socialCreditCode.put("otherAuthFile", otherAuthFileFuture.join());
        socialCreditCode.put("caFile", caFileFuture.join());
        socialCreditCode.put("otherFile", otherFileFuture.join());

        socialCreditCode.put("queryDate", DateUtil.format(new Date(), "yyyyMMdd"));
        return socialCreditCode;
    }
}
