package com.rongchen.byh.app.v2.mq.consumer;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.rabbitmq.client.Channel;
import com.rongchen.byh.app.constant.LoanType;
import com.rongchen.byh.app.constant.SourceMode;
import com.rongchen.byh.app.dao.*;
import com.rongchen.byh.app.entity.*;
import com.rongchen.byh.app.v2.entity.ApiCreditRelation;
import com.rongchen.byh.app.v2.entity.ApiPushRecord;
import com.rongchen.byh.app.v2.entity.RongOrderInfo;
import com.rongchen.byh.app.v2.service.*;
import com.rongchen.byh.app.v2.utils.Rong360ImageUtil;
import com.rongchen.byh.common.api.riskControl.dto.ApiCreditPreLoanAuditDto;
import com.rongchen.byh.common.api.riskControl.service.RiskControlService;
import com.rongchen.byh.common.api.riskControl.vo.PreLoanAuditVo;
import com.rongchen.byh.common.api.zifang.utils.IdCardUtil;
import com.rongchen.byh.common.rabbitmq.constants.QueueConstant;
import com.rongchen.byh.common.redis.util.CommonRedisUtil;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 融360授信异步处理
 * @date 2025/5/6 14:30:06
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class UserCreditApplyConsumer {
    @Resource
    IRongOrderInfoService rongOrderInfoService;
    @Resource
    IApiPushRecordService pushRecordService;
    @Resource
    UserDataMapper userDataMapper;
    @Resource
    ChannelDataMapper channelDataMapper;
    @Resource
    UserDetailMapper userDetailMapper;
    @Resource
    private CommonRedisUtil commonRedisUtil;
    @Resource
    private RedissonClient redissonClient;

    @Resource
    private UserLoanApplyMapper userLoanApplyMapper;

    @Resource
    IApiCreditRelationService creditRelationService;
    @Resource
    private RiskControlService riskControlService;
    @Resource
    private HrzxCreditLogMapper hrzxCreditLogMapper;
    @Resource
    private Rong360ImageUtil rong360ImageUtil;

    @RabbitListener(queues = QueueConstant.CREDIT_APPLY_QUEUE, ackMode = "MANUAL")
    public void receiver(String msg, Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
//        AirRiskControllerDto dto = null;
        String idempotencyKey = "未知";
        String traceId = UUID.fastUUID().toString();

        Map<String, String> contextMap = new HashMap<>();
        contextMap.put("traceId", traceId);
        MDC.setContextMap(contextMap);
        JSONObject orderInfo = null;
        JSONObject applyDetail = null;
        JSONObject addInfo = null;
        String channelSecret;
        try {
            try {
                JSONObject bizData = JSONObject.parseObject(msg);
                orderInfo = bizData.getJSONObject("orderInfo");
                applyDetail = bizData.getJSONObject("applyDetail");
                addInfo = bizData.getJSONObject("addInfo");
                channelSecret = bizData.getString("channel");
                if (orderInfo.isEmpty() || applyDetail.isEmpty() || addInfo.isEmpty() || channelSecret.isEmpty()) {
                    log.error("【授信进件监听器】接收到的 bizData 为空或缺少关键字段 (orderInfo, applyDetail,addInfo,channelSecret)，消息: {}", msg);
                    channel.basicNack(deliveryTag, false, false);
                    return;
                }
            } catch (Exception e) {
                log.error("【授信进件监听器】，进件接收消息失败，原始消息: {}, 错误: {}", msg, e.getMessage(), e);
                channel.basicNack(deliveryTag, false, false);
                return;
            }

            String keyPart;
            String orderNo = orderInfo.getString("order_no");
            if (StringUtil.isNotEmpty(orderNo)) {
                keyPart = "orderNo=" + orderNo;
            } else {
                log.error("【授信进件监听器】，进件接收orderNo字段缺少关键字段orderNo，原始消息: {}", msg);
                return;
            }
            idempotencyKey = String.format("credit_apply_queue_rong:orderNo=%s:channelSecret=%s:%s",
                    orderNo, channelSecret, keyPart);

            String statusKey = commonRedisUtil.buildKey("mq", "processed", "status", idempotencyKey);
            RBucket<String> statusBucket = redissonClient.getBucket(statusKey);

            if ("COMPLETED".equals(statusBucket.get())) {
                log.warn("【授信进件监听器】检测到重复消息（已处理完成），幂等键: {}。消息将被确认。", idempotencyKey);
                channel.basicAck(deliveryTag, false);
                return;
            }

            log.info("【授信进件监听器】开始处理消息，幂等键: {}, 原始消息: {}", idempotencyKey, msg);
            try {
                creditApply360(msg);
                statusBucket.set("COMPLETED", 1, TimeUnit.DAYS);
                log.info("【授信进件监听器】消息处理成功，幂等键: {}", idempotencyKey);
                channel.basicAck(deliveryTag, false);
                log.debug("【授信进件监听器】消息已确认 (ACK)，幂等键: {}, deliveryTag: {}", idempotencyKey, deliveryTag);
            } catch (Exception e) {
                log.error("【授信进件监听器】处理业务逻辑时发生异常，幂等键: {}, 错误: {}", idempotencyKey, e.getMessage(), e);
                channel.basicNack(deliveryTag, false, false);
                log.warn("【授信进件监听器】业务处理异常，消息已拒绝 (NACK)，幂等键: {}, deliveryTag: {}", idempotencyKey, deliveryTag);
            }

        } catch (IOException e) {
            log.error("【授信进件监听器】手动确认/拒绝消息时发生 IO 错误, 幂等键: {}, deliveryTag: {}. 错误: {}",
                    idempotencyKey, deliveryTag, e.getMessage(), e);
        } catch (Exception e) {
            log.error("【授信进件监听器】处理消息外层发生未知错误, 幂等键: {}, deliveryTag: {}. 错误: {}",
                    idempotencyKey, deliveryTag, e.getMessage(), e);
            try {
                channel.basicNack(deliveryTag, false, false);
                log.warn("【授信进件监听器】外层未知错误，消息已尝试拒绝 (NACK)，幂等键: {}, deliveryTag: {}", idempotencyKey, deliveryTag);
            } catch (IOException ioEx) {
                log.error("【授信进件监听器】外层未知错误，尝试拒绝消息时发生 IO 错误, 幂等键: {}, deliveryTag: {}. 错误: {}",
                        idempotencyKey, deliveryTag, ioEx.getMessage(), ioEx);
            }
        } finally {
            MDC.clear();
        }
    }

    private void creditApply360(String msg) throws Exception {
        JSONObject bizData = JSONObject.parseObject(msg);
        JSONObject orderInfo = bizData.getJSONObject("orderInfo");
        JSONObject applyDetail = bizData.getJSONObject("applyDetail");
        JSONObject addInfo = bizData.getJSONObject("addInfo");
        String channel = bizData.getString("channel");
        ChannelData channelData = channelDataMapper.selectBySecret(channel);
        //根据用户手机号查询用户ID
        String numberHouse = applyDetail.getString("phone_number_house");
        LambdaQueryWrapper<UserData> userWrapper = new LambdaQueryWrapper<>();
        userWrapper.eq(UserData::getMobile, numberHouse);
        UserData userData = userDataMapper.selectOne(userWrapper);
        //2.存userdata
        if (userData == null) {
            userData = new UserData();
            userData.setMobile(numberHouse);
            userData.setMobileMd(DigestUtil.md5Hex(numberHouse, StandardCharsets.UTF_8.name()));
            userData.setChannelId(channelData.getId());
            userData.setSourceMode(SourceMode.RONG360);
            userData.setIp(applyDetail.getString("ip_address"));
            userData.setCreateTime(new Date());
            userDataMapper.insert(userData);
        }
        Long userId = userData.getId();
        //3.存userdetail
        UserDetail newDetail = userDetailMapper.queryByUserId(userId);
        if (newDetail == null) {
            newDetail = new UserDetail();
            newDetail.setUserId(userData.getId());
            newDetail.setWebName(applyDetail.getString("bureau_user_name"));
            newDetail.setWebIdCard(applyDetail.getString("user_id"));
            //此处剩web_two_elements，risk_level，head_url
            //添加身份证信息
            newDetail.setAppName(addInfo.getString("Name_OCR"));
            newDetail.setSex(addInfo.getString("ID_Sex_OCR"));
            newDetail.setNation(addInfo.getString("ID_Ethnic_OCR"));
            String birthDateOcr = addInfo.getString("ID_BirthDate_OCR");
            SimpleDateFormat simBir = new SimpleDateFormat("yyyy/MM/dd");
            newDetail.setBirth(simBir.format(simBir.parse(birthDateOcr)));
            newDetail.setAddress(addInfo.getString("ID_Address_OCR"));
            newDetail.setIdNumber(addInfo.getString("ID_Number_OCR"));
            newDetail.setAuthority(addInfo.getString("ID_Issue_Org_OCR"));
            String idDueTimeOcr = addInfo.getString("ID_Due_time_OCR");
            String[] split = idDueTimeOcr.split("-");
            String startTime = split[0];
            String endTime = split[1];
            String resultStr = startTime.substring(0, 4) + "." + startTime.substring(4, 6) + "." + startTime.substring(6) + "-" + endTime.substring(0, 4) + "." + endTime.substring(4, 6) + "." + endTime.substring(6);
            newDetail.setValidDate(resultStr);
            // 身份证正面照
            JSONArray id_positive = addInfo.getJSONArray("ID_Positive");
            Map<String, CompletableFuture<String>> imageFutureMap = new HashMap<>();
            CompletableFuture<String> frontFuture = rong360ImageUtil.downloadImage(orderInfo.getString("order_no"), id_positive.getString(0), IdUtil.fastSimpleUUID(), userId);
            imageFutureMap.put("front", frontFuture);
            // 身份证反面照
            JSONArray id_negative = addInfo.getJSONArray("ID_Negative");
            CompletableFuture<String> backFuture = rong360ImageUtil.downloadImage(orderInfo.getString("order_no"), id_negative.getString(0), "jpg", userId);
            imageFutureMap.put("back", backFuture);
            // 人脸照片
            JSONArray photo_assay = addInfo.getJSONArray("photo_assay");
            CompletableFuture<String> faceFuture = rong360ImageUtil.downloadImage(orderInfo.getString("order_no"), photo_assay.getString(0), "jpg", userId);
            imageFutureMap.put("face", faceFuture);
            // 三种照片保证都上传到oss
            rong360ImageUtil.uploadUserInfoOss(newDetail, imageFutureMap);
            newDetail.setFaceTime(DateUtil.now());
            //此处剩ocr_result，face_url，face_score，face_confidence，face_source，face_time，face_result
            newDetail.setEducationLevel(applyDetail.getString("user_education"));
            String userMarriage = applyDetail.getString("user_marriage");
            newDetail.setMaritalStatus(userMarriage);
            String housingNew = applyDetail.getString("Housing_new");
            if (housingNew.equals("1")) {
                newDetail.setHouseStatus("2");
            } else if (housingNew.equals("2")) {
                newDetail.setHouseStatus("3");
            } else {
                newDetail.setHouseStatus("5");
            }
            String addrDetail = addInfo.getString("addr_detail");
            String[] addrArr = addrDetail.split(" ");
            newDetail.setCustAddressProvice(addrArr[0]);
            newDetail.setCustAddressCity(addrArr[1]);
            newDetail.setCustAddressCounty(addrArr[2]);
            newDetail.setCustAddress(addrArr[3]);
            //设置用户月收入信息
            Float userIncomeByCard = applyDetail.getFloat("user_income_by_card");
            if (userIncomeByCard < 3000f) {
                newDetail.setIncomeMonth("1");
            } else if (userIncomeByCard < 5000f) {
                newDetail.setIncomeMonth("2");
            } else if (userIncomeByCard < 10000f) {
                newDetail.setIncomeMonth("3");
            } else if (userIncomeByCard < 20000f) {
                newDetail.setIncomeMonth("4");
            } else if (userIncomeByCard < 30000f) {
                newDetail.setIncomeMonth("5");
            } else if (userIncomeByCard < 50000f) {
                newDetail.setIncomeMonth("6");
            } else {
                newDetail.setIncomeMonth("7");
            }
            //添加紧急联系人信息
            newDetail.setEmergencyNameOne((addInfo.getString("emergency_contact_personA_name")));
            newDetail.setEmergencyMobileOne(addInfo.getString("emergency_contact_personA_phone"));
            newDetail.setRelationshipOne(addInfo.getString("emergency_contact_personA_relationship"));
            newDetail.setEmergencyNameTwo(addInfo.getString("emergency_contact_personB_name"));
            newDetail.setEmergencyMobileTwo(addInfo.getString("emergency_contact_personB_phone"));
            newDetail.setRelationshipTwo(addInfo.getString("emergency_contact_personB_relationship"));
            newDetail.setFormFlag(1);
            newDetail.setFormTime(new Date().toString());
            newDetail.setPhoneNumber(numberHouse);
            //mobile_start_time,wifi_sensitive,address_book_num,address_book_mobile_num,address_book_sensitive,contact_operator,overdue_message_num
            JSONObject contacts = addInfo.getJSONObject("contacts");
            JSONObject appLocation = contacts.getJSONObject("app_location");
            newDetail.setLongitude(appLocation.getString("lon"));
            newDetail.setLatitude(appLocation.getString("lat"));
            JSONObject device_info_all = addInfo.getJSONObject("device_info_all");
            newDetail.setDeviceBrand(applyDetail.getString("phone_brand"));
            //network_type
            newDetail.setDevAlias(applyDetail.getString("device_info"));
            newDetail.setDeviceId(applyDetail.getString("device_num"));
            newDetail.setClientIp(applyDetail.getString("ip_address"));
            //coordinate_type,gps_city,lbs_address,
            newDetail.setGpsAddress(applyDetail.getString("gps_location"));
            newDetail.setOs(applyDetail.getString("platform"));
            //os_version
            newDetail.setCreateTime(new Date());
            newDetail.setCompanyName(addInfo.getString("company_name"));
            Integer isOpType = Integer.getInteger(applyDetail.getString("is_op_type"));
            if (isOpType == 1) {
                newDetail.setProfessional(2);
            } else if (isOpType == 2) {
                newDetail.setProfessional(3);
            } else if (isOpType == 4) {
                newDetail.setProfessional(1);
            } else {
                newDetail.setProfessional(4);
            }
            userDetailMapper.insert(newDetail);
        }
        //4.存用户订单基本信息
        Integer outUserId = orderInfo.getInteger("user_id");
        RongOrderInfo info = new RongOrderInfo();
        info.setOrderNo(orderInfo.getString("order_no"));
        info.setUserId(userId);
        info.setOutUserId(outUserId);
        info.setOrderTime(orderInfo.getLong("order_time"));
        info.setStatus(orderInfo.getInteger("status"));
        info.setBank(orderInfo.getString("bank"));
        info.setProduct(orderInfo.getString("product"));
        info.setProductId(orderInfo.getInteger("product_id"));
        info.setUserGroupId(orderInfo.getInteger("user_group_id"));
        rongOrderInfoService.insert(info);
        //5.修改api_push_record
        ApiPushRecord apiPush = pushRecordService.selectByOrderNo(orderInfo.getString("order_no"));
        if (apiPush != null) {
            apiPush.setOutUserId(outUserId);
            apiPush.setUserId(userId);
            apiPush.setCreateTime(new Date());
            apiPush.setChannelId(channelData.getId());
            pushRecordService.update(apiPush);
        }
        ApiCreditPreLoanAuditDto apiCreditPreLoanAuditDto = buildCreditDto(newDetail, userData.getMobile());
        //创建user_loan_apply
        UserLoanApply apply = new UserLoanApply();
        apply.setUserId(userId);
        apply.setCreditId(apiCreditPreLoanAuditDto.getCredit_id());
        apply.setApplyType(LoanType.CHECK);
        apply.setOnlineType(SourceMode.RONG360);
        userLoanApplyMapper.insert(apply);
        JSONObject creditRelation = bizData.getJSONObject("creditRelation");
        ApiCreditRelation apiCreditRelation = creditRelation.toJavaObject(ApiCreditRelation.class);
        apiCreditRelation.setUserLoanApplyId(apply.getId());
        creditRelationService.updateById(apiCreditRelation);
        // 调用大数据风控
        PreLoanAuditVo preLoanAuditVo = riskControlService.rong360CreditPreLoanAudit(apiCreditPreLoanAuditDto);
        HrzxCreditLog addHrzxCreditLog = new HrzxCreditLog();
        addHrzxCreditLog.setUserId(userId);
        addHrzxCreditLog.setStatus(preLoanAuditVo.getResult());
        addHrzxCreditLog.setResult(preLoanAuditVo.getMessage());
        hrzxCreditLogMapper.insert(addHrzxCreditLog);
    }

    private ApiCreditPreLoanAuditDto buildCreditDto(UserDetail detail, String mobile) {
        ApiCreditPreLoanAuditDto creditPreLoanAuditDto = new ApiCreditPreLoanAuditDto();
        creditPreLoanAuditDto.setIdcard_no(detail.getIdNumber());
        creditPreLoanAuditDto.setMobile(mobile);
        creditPreLoanAuditDto.setName(detail.getAppName());
        creditPreLoanAuditDto.setCredit_id(IdUtil.fastSimpleUUID());
        creditPreLoanAuditDto.setCredit_time(DateUtil.now());
        creditPreLoanAuditDto.setNation_ocr(IdCardUtil.nationOcr(detail.getNation()));
        creditPreLoanAuditDto.setIdcard_address_ocr(detail.getAddress());
        String[] split = detail.getValidDate().split("-");
        creditPreLoanAuditDto.setCert_start(split[0].replace(".", "-"));
        if ("长期".equals(split[1])) {
            split[1] = "2099.12.31";
        }
        creditPreLoanAuditDto.setCert_end(split[1].replace(".", "-"));
        JSONObject socialCreditCode = new JSONObject();
        socialCreditCode.put("requestNo", IdUtil.fastSimpleUUID());
        socialCreditCode.put("queryName", detail.getAppName());
        socialCreditCode.put("queryIdNoType", "01");
        socialCreditCode.put("queryIdNo", detail.getIdNumber());
        socialCreditCode.put("queryPhone", mobile);
        socialCreditCode.put("veriFaceTime", DateUtil.now());
        socialCreditCode.put("authTime", DateUtil.now());

        socialCreditCode.put("requestNo", UUID.fastUUID().toString(true));

        socialCreditCode.put("queryDate", DateUtil.format(new Date(), "yyyyMMdd"));
        JSONObject company = new JSONObject();
        company.put("social_credit_code", Base64.getEncoder().encodeToString(socialCreditCode.toJSONString()
                .getBytes(StandardCharsets.UTF_8)));
        creditPreLoanAuditDto.setCompany_info(company);
        return creditPreLoanAuditDto;
    }


}
