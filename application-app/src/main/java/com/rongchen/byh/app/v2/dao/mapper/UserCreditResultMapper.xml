<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rongchen.byh.app.v2.dao.UserCreditResultMapper">

    <resultMap id="BaseResultMap" type="com.rongchen.byh.app.v2.entity.UserCreditResult">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="creditStatus" column="credit_status" jdbcType="INTEGER"/>
            <result property="creditTime" column="credit_time" jdbcType="TIMESTAMP"/>
            <result property="deleted" column="deleted" jdbcType="INTEGER"/>
            <result property="deleteTime" column="delete_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,user_id,credit_status,
        credit_time,deleted,delete_time,
        create_time,update_time
    </sql>
    <select id="selectByUserId" resultType="com.rongchen.byh.app.v2.entity.UserCreditResult">
        select <include refid="Base_Column_List" />
        from user_credit_result
        where user_id = #{userId}
    </select>
</mapper>
